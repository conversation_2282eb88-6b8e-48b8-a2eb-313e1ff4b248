#!/usr/bin/env python3
"""Test Tavily tools specifically."""

import sys
from pathlib import Path

# Add src to path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

print("🧪 Testing Tavily tools...")

try:
    print("1. Testing Tavily tools import...")
    from pyframeworks_assistant.tools.tavily_tools import (
        TavilySearchTool, 
        TavilyExtractTool, 
        TavilyCrawlTool,
        create_tavily_search_tool,
        get_tavily_tools
    )
    print("✅ Tavily tools imported successfully")
    
    print("2. Testing tool creation...")
    search_tool = TavilySearchTool(api_key="test_key")
    print(f"✅ TavilySearchTool created - name: {search_tool.name}")
    print(f"   Description: {search_tool.description}")
    
    extract_tool = TavilyExtractTool(api_key="test_key")
    print(f"✅ TavilyExtractTool created - name: {extract_tool.name}")
    
    crawl_tool = TavilyCrawlTool(api_key="test_key")
    print(f"✅ TavilyCrawlTool created - name: {crawl_tool.name}")
    
    print("3. Testing factory function...")
    factory_tool = create_tavily_search_tool("test_key")
    print(f"✅ Factory tool created - name: {factory_tool.name}")
    print(f"   Type: {type(factory_tool).__name__}")
    
    print("4. Testing get_tavily_tools function...")
    all_tools = get_tavily_tools("test_key")
    print(f"✅ Got {len(all_tools)} tools from factory")
    for i, tool in enumerate(all_tools):
        print(f"   {i+1}. {tool.name} ({type(tool).__name__})")
    
    print("\n🎉 All Tavily tests passed!")
    
except Exception as e:
    print(f"❌ Tavily test failed: {e}")
    import traceback
    traceback.print_exc()
