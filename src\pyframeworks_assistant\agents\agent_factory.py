"""Agent factory for creating and managing GAAPF agents."""

from typing import Dict, List, Optional, Any
from langchain_core.language_models import BaseChatModel

from .base_agent import BaseAgent, AgentRole
from .knowledge_agents import (
    InstructorAgent,
    DocumentationExpertAgent,
    ResearchAssistantAgent,
    KnowledgeSynthesizerAgent,
)
from .practice_agents import (
    CodeAssistantAgent,
    PracticeFacilitatorAgent,
    ProjectGuideAgent,
    TroubleshooterAgent,
)
from .support_agents import (
    MentorAgent,
    MotivationalCoachAgent,
)
from .assessment_agents import (
    AssessmentAgent,
    ProgressTrackerAgent,
)


class AgentFactory:
    """Factory for creating and managing GAAPF agents."""
    
    # Agent class mapping
    AGENT_CLASSES = {
        AgentRole.INSTRUCTOR: InstructorAgent,
        AgentR<PERSON>.DOCUMENTATION_EXPERT: DocumentationExpertAgent,
        AgentRole.RESEARCH_ASSISTANT: ResearchAssistantAgent,
        AgentRole.KNOWLEDGE_SYNTHESIZER: KnowledgeSynthesizerAgent,
        AgentRole.CODE_ASSISTANT: CodeAssistantAgent,
        AgentRole.PRACTICE_FACILITATOR: PracticeFacilitatorAgent,
        AgentRole.PROJECT_GUIDE: ProjectGuideAgent,
        AgentRole.TROUBLESHOOTER: TroubleshooterAgent,
        AgentRole.MENTOR: MentorAgent,
        AgentRole.MOTIVATIONAL_COACH: MotivationalCoachAgent,
        AgentRole.ASSESSMENT: AssessmentAgent,
        AgentRole.PROGRESS_TRACKER: ProgressTrackerAgent,
    }
    
    def __init__(self, llm: BaseChatModel, tools: Optional[Dict[str, List[Any]]] = None):
        """Initialize the agent factory.
        
        Args:
            llm: The language model to use for all agents
            tools: Optional dictionary mapping agent roles to their tools
        """
        self.llm = llm
        self.tools = tools or {}
        self._agent_cache: Dict[AgentRole, BaseAgent] = {}
    
    def create_agent(self, role: AgentRole, tools: Optional[List[Any]] = None) -> BaseAgent:
        """Create an agent of the specified role.
        
        Args:
            role: The role of the agent to create
            tools: Optional tools specific to this agent instance
            
        Returns:
            The created agent instance
        """
        if role not in self.AGENT_CLASSES:
            raise ValueError(f"Unknown agent role: {role}")
        
        agent_class = self.AGENT_CLASSES[role]
        agent_tools = tools or self.tools.get(role, [])
        
        return agent_class(llm=self.llm, tools=agent_tools)
    
    def get_or_create_agent(self, role: AgentRole, tools: Optional[List[Any]] = None) -> BaseAgent:
        """Get an existing agent from cache or create a new one.
        
        Args:
            role: The role of the agent to get/create
            tools: Optional tools specific to this agent instance
            
        Returns:
            The agent instance
        """
        if role not in self._agent_cache:
            self._agent_cache[role] = self.create_agent(role, tools)
        
        return self._agent_cache[role]
    
    def create_constellation_agents(self, agent_roles: List[AgentRole]) -> Dict[AgentRole, BaseAgent]:
        """Create a set of agents for a constellation.
        
        Args:
            agent_roles: List of agent roles to create
            
        Returns:
            Dictionary mapping roles to agent instances
        """
        agents = {}
        for role in agent_roles:
            agents[role] = self.get_or_create_agent(role)
        
        return agents
    
    def get_available_roles(self) -> List[AgentRole]:
        """Get list of all available agent roles."""
        return list(self.AGENT_CLASSES.keys())
    
    def get_agent_info(self, role: AgentRole) -> Dict[str, Any]:
        """Get information about an agent role.
        
        Args:
            role: The agent role to get info for
            
        Returns:
            Dictionary with agent information
        """
        if role not in self.AGENT_CLASSES:
            raise ValueError(f"Unknown agent role: {role}")
        
        agent_class = self.AGENT_CLASSES[role]
        
        # Create a temporary instance to get domain info
        temp_agent = agent_class(llm=self.llm)
        
        return {
            "role": role.value,
            "domain": temp_agent.domain.value,
            "class_name": agent_class.__name__,
            "description": agent_class.__doc__ or "No description available"
        }
    
    def clear_cache(self) -> None:
        """Clear the agent cache."""
        self._agent_cache.clear()
    
    def get_cached_agents(self) -> Dict[AgentRole, BaseAgent]:
        """Get all cached agents."""
        return self._agent_cache.copy()
    
    @classmethod
    def get_agents_by_domain(cls, domain: str) -> List[AgentRole]:
        """Get all agent roles in a specific domain.
        
        Args:
            domain: The domain to filter by
            
        Returns:
            List of agent roles in the domain
        """
        domain_agents = []
        
        # We need to create temporary instances to check domains
        # This is not ideal but necessary without storing domain info separately
        temp_llm = None  # We'll need to handle this better in practice
        
        for role, agent_class in cls.AGENT_CLASSES.items():
            # For now, we'll use a simple mapping based on the agent classes
            if domain == "knowledge" and agent_class in [
                InstructorAgent, DocumentationExpertAgent, 
                ResearchAssistantAgent, KnowledgeSynthesizerAgent
            ]:
                domain_agents.append(role)
            elif domain == "practice" and agent_class in [
                CodeAssistantAgent, PracticeFacilitatorAgent,
                ProjectGuideAgent, TroubleshooterAgent
            ]:
                domain_agents.append(role)
            elif domain == "support" and agent_class in [
                MentorAgent, MotivationalCoachAgent
            ]:
                domain_agents.append(role)
            elif domain == "assessment" and agent_class in [
                AssessmentAgent, ProgressTrackerAgent
            ]:
                domain_agents.append(role)
        
        return domain_agents
