"""Framework configuration and module definitions for GAAPF system."""

from enum import Enum
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field


class SupportedFrameworks(str, Enum):
    """Supported AI frameworks enumeration."""
    LANGCHAIN = "langchain"
    LANGGRAPH = "langgraph"
    CREWAI = "crewai"  # Future support
    AUTOGEN = "autogen"  # Future support
    LLAMAINDEX = "llamaindex"  # Future support


class ModuleConfig(BaseModel):
    """Configuration for a learning module within a framework."""
    
    module_id: str = Field(..., description="Unique module identifier")
    title: str = Field(..., description="Module display title")
    description: str = Field(..., description="Module description")
    difficulty_level: int = Field(..., ge=1, le=5, description="Difficulty level (1-5)")
    estimated_duration_minutes: int = Field(..., ge=5, description="Estimated completion time")
    prerequisites: List[str] = Field(default_factory=list, description="Required prerequisite modules")
    learning_objectives: List[str] = Field(default_factory=list, description="Learning objectives")
    topics: List[str] = Field(default_factory=list, description="Topics covered")
    hands_on_exercises: bool = Field(True, description="Whether module includes hands-on exercises")
    assessment_available: bool = Field(True, description="Whether assessment is available")


class FrameworkConfig(BaseModel):
    """Configuration for a supported AI framework."""
    
    framework_id: str = Field(..., description="Framework identifier")
    name: str = Field(..., description="Framework display name")
    description: str = Field(..., description="Framework description")
    version: str = Field(..., description="Supported version")
    documentation_url: str = Field(..., description="Official documentation URL")
    github_url: Optional[str] = Field(None, description="GitHub repository URL")
    
    # Learning path configuration
    modules: Dict[str, ModuleConfig] = Field(default_factory=dict, description="Available modules")
    learning_paths: Dict[str, List[str]] = Field(default_factory=dict, description="Predefined learning paths")
    
    # Framework-specific settings
    installation_command: str = Field(..., description="Installation command")
    import_examples: List[str] = Field(default_factory=list, description="Common import examples")
    key_concepts: List[str] = Field(default_factory=list, description="Key concepts to learn")


# LangChain Framework Configuration
LANGCHAIN_MODULES = {
    "lc_basics": ModuleConfig(
        module_id="lc_basics",
        title="LangChain Fundamentals",
        description="Introduction to LangChain core concepts and basic usage",
        difficulty_level=1,
        estimated_duration_minutes=45,
        prerequisites=[],
        learning_objectives=[
            "Understand LangChain architecture",
            "Learn about LLMs and Chat Models",
            "Create basic chains",
            "Handle prompts and templates"
        ],
        topics=[
            "LangChain overview",
            "LLM integration",
            "Prompt templates",
            "Basic chains",
            "Output parsers"
        ]
    ),
    "lc_chains": ModuleConfig(
        module_id="lc_chains",
        title="Advanced Chains and Pipelines",
        description="Building complex chains and processing pipelines",
        difficulty_level=2,
        estimated_duration_minutes=60,
        prerequisites=["lc_basics"],
        learning_objectives=[
            "Build sequential chains",
            "Create conditional logic",
            "Handle chain composition",
            "Implement error handling"
        ],
        topics=[
            "Sequential chains",
            "Router chains",
            "Transform chains",
            "Chain composition",
            "Error handling"
        ]
    ),
    "lc_memory": ModuleConfig(
        module_id="lc_memory",
        title="Memory and Context Management",
        description="Managing conversation memory and context in LangChain",
        difficulty_level=2,
        estimated_duration_minutes=50,
        prerequisites=["lc_basics"],
        learning_objectives=[
            "Implement conversation memory",
            "Manage context windows",
            "Use different memory types",
            "Handle memory persistence"
        ],
        topics=[
            "Conversation buffer memory",
            "Summary memory",
            "Vector store memory",
            "Memory persistence",
            "Context management"
        ]
    ),
    "lc_agents": ModuleConfig(
        module_id="lc_agents",
        title="Agents and Tools",
        description="Building intelligent agents with tool integration",
        difficulty_level=3,
        estimated_duration_minutes=75,
        prerequisites=["lc_chains"],
        learning_objectives=[
            "Create LangChain agents",
            "Integrate external tools",
            "Handle agent reasoning",
            "Implement custom tools"
        ],
        topics=[
            "Agent types",
            "Tool integration",
            "Agent executors",
            "Custom tools",
            "Agent reasoning"
        ]
    ),
    "lc_rag": ModuleConfig(
        module_id="lc_rag",
        title="Retrieval Augmented Generation (RAG)",
        description="Building RAG systems with LangChain",
        difficulty_level=4,
        estimated_duration_minutes=90,
        prerequisites=["lc_memory", "lc_agents"],
        learning_objectives=[
            "Build RAG pipelines",
            "Implement vector stores",
            "Handle document processing",
            "Optimize retrieval"
        ],
        topics=[
            "Document loaders",
            "Text splitters",
            "Vector stores",
            "Retrieval chains",
            "RAG optimization"
        ]
    )
}

# LangGraph Framework Configuration
LANGGRAPH_MODULES = {
    "lg_basics": ModuleConfig(
        module_id="lg_basics",
        title="LangGraph Fundamentals",
        description="Introduction to stateful multi-agent workflows",
        difficulty_level=2,
        estimated_duration_minutes=60,
        prerequisites=["lc_basics"],
        learning_objectives=[
            "Understand LangGraph concepts",
            "Create basic graphs",
            "Manage state",
            "Handle node execution"
        ],
        topics=[
            "Graph concepts",
            "State management",
            "Node creation",
            "Edge definition",
            "Graph execution"
        ]
    ),
    "lg_agents": ModuleConfig(
        module_id="lg_agents",
        title="Multi-Agent Coordination",
        description="Building coordinated multi-agent systems",
        difficulty_level=4,
        estimated_duration_minutes=90,
        prerequisites=["lg_basics", "lc_agents"],
        learning_objectives=[
            "Design agent workflows",
            "Implement agent handoffs",
            "Manage shared state",
            "Handle agent coordination"
        ],
        topics=[
            "Agent workflows",
            "State sharing",
            "Agent handoffs",
            "Coordination patterns",
            "Workflow optimization"
        ]
    )
}

# Framework configurations
FRAMEWORK_CONFIGS = {
    SupportedFrameworks.LANGCHAIN: FrameworkConfig(
        framework_id="langchain",
        name="LangChain",
        description="Framework for developing applications powered by language models",
        version="0.3.25+",
        documentation_url="https://python.langchain.com/",
        github_url="https://github.com/langchain-ai/langchain",
        modules=LANGCHAIN_MODULES,
        learning_paths={
            "beginner": ["lc_basics", "lc_chains", "lc_memory"],
            "intermediate": ["lc_basics", "lc_chains", "lc_memory", "lc_agents"],
            "advanced": ["lc_basics", "lc_chains", "lc_memory", "lc_agents", "lc_rag"],
            "rag_focused": ["lc_basics", "lc_memory", "lc_rag"],
            "agent_focused": ["lc_basics", "lc_chains", "lc_agents"]
        },
        installation_command="pip install langchain",
        import_examples=[
            "from langchain.llms import OpenAI",
            "from langchain.chat_models import ChatOpenAI",
            "from langchain.prompts import PromptTemplate",
            "from langchain.chains import LLMChain"
        ],
        key_concepts=[
            "LLMs and Chat Models",
            "Prompts and Templates",
            "Chains and Pipelines",
            "Memory Management",
            "Agents and Tools",
            "Vector Stores",
            "Document Processing"
        ]
    ),
    SupportedFrameworks.LANGGRAPH: FrameworkConfig(
        framework_id="langgraph",
        name="LangGraph",
        description="Library for building stateful, multi-actor applications with LLMs",
        version="0.4.7+",
        documentation_url="https://langchain-ai.github.io/langgraph/",
        github_url="https://github.com/langchain-ai/langgraph",
        modules=LANGGRAPH_MODULES,
        learning_paths={
            "beginner": ["lg_basics"],
            "intermediate": ["lg_basics", "lg_agents"],
            "advanced": ["lg_basics", "lg_agents"]
        },
        installation_command="pip install langgraph",
        import_examples=[
            "from langgraph.graph import StateGraph",
            "from langgraph.prebuilt import ToolExecutor",
            "from langgraph.checkpoint.sqlite import SqliteSaver"
        ],
        key_concepts=[
            "State Graphs",
            "Node Functions",
            "Edge Conditions",
            "State Management",
            "Checkpointing",
            "Multi-Agent Workflows"
        ]
    )
}


def get_framework_config(framework: SupportedFrameworks) -> FrameworkConfig:
    """Get configuration for a specific framework."""
    return FRAMEWORK_CONFIGS[framework]


def get_module_config(framework: SupportedFrameworks, module_id: str) -> Optional[ModuleConfig]:
    """Get configuration for a specific module within a framework."""
    framework_config = get_framework_config(framework)
    return framework_config.modules.get(module_id)


def get_learning_path(framework: SupportedFrameworks, path_name: str) -> List[str]:
    """Get a predefined learning path for a framework."""
    framework_config = get_framework_config(framework)
    return framework_config.learning_paths.get(path_name, [])


def get_available_modules(framework: SupportedFrameworks) -> List[str]:
    """Get list of available modules for a framework."""
    framework_config = get_framework_config(framework)
    return list(framework_config.modules.keys())
