"""Learning and assessment tools for GAAPF."""

import json
import random
from typing import Dict, List, Optional, Any, Type
from pathlib import Path
from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field


class AssessmentInput(BaseModel):
    """Input for assessment tool."""
    user_id: str = Field(..., description="User identifier")
    topic: str = Field(..., description="Topic to assess")
    difficulty: str = Field("intermediate", description="Difficulty level (beginner, intermediate, advanced)")
    question_count: int = Field(5, description="Number of questions")


class ProgressInput(BaseModel):
    """Input for progress tool."""
    user_id: str = Field(..., description="User identifier")
    action: str = Field(..., description="Action: 'get', 'update', or 'summary'")
    data: Optional[Dict[str, Any]] = Field(None, description="Data for update action")


class ExerciseGeneratorInput(BaseModel):
    """Input for exercise generator tool."""
    topic: str = Field(..., description="Topic for exercise")
    exercise_type: str = Field("coding", description="Type of exercise (coding, conceptual, practical)")
    difficulty: str = Field("intermediate", description="Difficulty level")
    framework: str = Field("langchain", description="Framework context")


class AssessmentTool(BaseTool):
    """Tool for generating and managing assessments."""

    name: str = "assessment"
    description: str = "Generate assessments and quizzes for learning topics"
    args_schema: Type[BaseModel] = AssessmentInput
    
    def __init__(self):
        super().__init__()
        self.assessments_dir = Path("assessments")
        self.assessments_dir.mkdir(exist_ok=True)
        
        # Load question banks
        self.question_banks = self._load_question_banks()
    
    def _load_question_banks(self) -> Dict[str, List[Dict[str, Any]]]:
        """Load question banks for different topics."""
        return {
            "langchain": [
                {
                    "question": "What is the primary purpose of LangChain?",
                    "type": "multiple_choice",
                    "options": [
                        "A) Database management",
                        "B) Building applications with language models",
                        "C) Web scraping",
                        "D) Image processing"
                    ],
                    "correct": "B",
                    "difficulty": "beginner",
                    "explanation": "LangChain is a framework for developing applications powered by language models."
                },
                {
                    "question": "Which component is used to format inputs to language models in LangChain?",
                    "type": "multiple_choice",
                    "options": [
                        "A) Chains",
                        "B) Agents",
                        "C) Prompt Templates",
                        "D) Memory"
                    ],
                    "correct": "C",
                    "difficulty": "beginner",
                    "explanation": "Prompt Templates are used to format and structure inputs to language models."
                },
                {
                    "question": "Explain the difference between LLMChain and SequentialChain in LangChain.",
                    "type": "open_ended",
                    "difficulty": "intermediate",
                    "sample_answer": "LLMChain is a simple chain that combines a prompt template with an LLM, while SequentialChain allows chaining multiple chains together where the output of one becomes the input of the next."
                },
                {
                    "question": "Write a simple LangChain code example that creates a prompt template and uses it with an LLM.",
                    "type": "coding",
                    "difficulty": "intermediate",
                    "sample_code": """
from langchain.llms import OpenAI
from langchain.prompts import PromptTemplate
from langchain.chains import LLMChain

# Create prompt template
template = "Tell me a {adjective} fact about {topic}"
prompt = PromptTemplate(
    input_variables=["adjective", "topic"],
    template=template
)

# Create LLM and chain
llm = OpenAI()
chain = LLMChain(llm=llm, prompt=prompt)

# Use the chain
result = chain.run(adjective="interesting", topic="Python")
print(result)
"""
                }
            ],
            "langgraph": [
                {
                    "question": "What is the main advantage of LangGraph over traditional LangChain chains?",
                    "type": "multiple_choice",
                    "options": [
                        "A) Faster execution",
                        "B) Stateful, multi-actor applications",
                        "C) Better documentation",
                        "D) Lower cost"
                    ],
                    "correct": "B",
                    "difficulty": "intermediate",
                    "explanation": "LangGraph enables building stateful, multi-actor applications with complex workflows."
                },
                {
                    "question": "Describe how state management works in LangGraph.",
                    "type": "open_ended",
                    "difficulty": "advanced",
                    "sample_answer": "LangGraph manages state through a StateGraph where each node can read from and write to a shared state object. The state persists across node executions and can be checkpointed for persistence."
                }
            ]
        }
    
    def _run(self, user_id: str, topic: str, difficulty: str = "intermediate", question_count: int = 5) -> str:
        """Generate an assessment."""
        try:
            # Get questions for the topic
            topic_lower = topic.lower()
            if topic_lower not in self.question_banks:
                return f"No questions available for topic: {topic}"
            
            questions = self.question_banks[topic_lower]
            
            # Filter by difficulty
            filtered_questions = [
                q for q in questions 
                if q.get("difficulty", "intermediate") == difficulty
            ]
            
            if not filtered_questions:
                # Fall back to all questions if no difficulty match
                filtered_questions = questions
            
            # Select random questions
            selected_questions = random.sample(
                filtered_questions, 
                min(question_count, len(filtered_questions))
            )
            
            # Format assessment
            assessment = {
                "user_id": user_id,
                "topic": topic,
                "difficulty": difficulty,
                "questions": selected_questions,
                "created_at": "now"
            }
            
            # Save assessment
            assessment_file = self.assessments_dir / f"{user_id}_{topic}_{difficulty}_assessment.json"
            with open(assessment_file, 'w') as f:
                json.dump(assessment, f, indent=2)
            
            # Format for display
            result = [f"**Assessment: {topic.title()} ({difficulty})**\n"]
            
            for i, question in enumerate(selected_questions, 1):
                result.append(f"**Question {i}:**")
                result.append(question["question"])
                
                if question["type"] == "multiple_choice":
                    result.append("Options:")
                    for option in question["options"]:
                        result.append(f"  {option}")
                
                result.append("")
            
            result.append(f"Assessment saved to: {assessment_file}")
            
            return "\n".join(result)
        
        except Exception as e:
            return f"Error generating assessment: {str(e)}"
    
    async def _arun(self, user_id: str, topic: str, difficulty: str = "intermediate", question_count: int = 5) -> str:
        """Async version of assessment generation."""
        return self._run(user_id, topic, difficulty, question_count)


class ProgressTool(BaseTool):
    """Tool for tracking and managing learning progress."""

    name: str = "progress"
    description: str = "Track and manage user learning progress"
    args_schema: Type[BaseModel] = ProgressInput
    
    def __init__(self):
        super().__init__()
        self.progress_dir = Path("progress_data")
        self.progress_dir.mkdir(exist_ok=True)
    
    def _run(self, user_id: str, action: str, data: Optional[Dict[str, Any]] = None) -> str:
        """Manage user progress."""
        try:
            progress_file = self.progress_dir / f"{user_id}_progress.json"
            
            if action == "get":
                return self._get_progress(progress_file)
            elif action == "update":
                return self._update_progress(progress_file, data or {})
            elif action == "summary":
                return self._get_progress_summary(progress_file)
            else:
                return f"Unknown action: {action}. Use 'get', 'update', or 'summary'"
        
        except Exception as e:
            return f"Error managing progress: {str(e)}"
    
    def _get_progress(self, progress_file: Path) -> str:
        """Get user progress."""
        if not progress_file.exists():
            return "No progress data found for user"
        
        with open(progress_file, 'r') as f:
            progress = json.load(f)
        
        result = ["**Learning Progress:**\n"]
        
        for topic, topic_progress in progress.items():
            if isinstance(topic_progress, dict):
                completion = topic_progress.get("completion", 0)
                last_activity = topic_progress.get("last_activity", "Never")
                result.append(f"- **{topic}**: {completion}% complete (Last: {last_activity})")
        
        return "\n".join(result)
    
    def _update_progress(self, progress_file: Path, data: Dict[str, Any]) -> str:
        """Update user progress."""
        # Load existing progress
        if progress_file.exists():
            with open(progress_file, 'r') as f:
                progress = json.load(f)
        else:
            progress = {}
        
        # Update with new data
        for topic, topic_data in data.items():
            if topic not in progress:
                progress[topic] = {}
            
            progress[topic].update(topic_data)
            progress[topic]["last_activity"] = "now"
        
        # Save updated progress
        with open(progress_file, 'w') as f:
            json.dump(progress, f, indent=2)
        
        return f"Progress updated for {len(data)} topics"
    
    def _get_progress_summary(self, progress_file: Path) -> str:
        """Get progress summary."""
        if not progress_file.exists():
            return "No progress data found for user"
        
        with open(progress_file, 'r') as f:
            progress = json.load(f)
        
        total_topics = len(progress)
        completed_topics = len([
            topic for topic, data in progress.items()
            if isinstance(data, dict) and data.get("completion", 0) >= 100
        ])
        
        avg_completion = sum([
            data.get("completion", 0) for data in progress.values()
            if isinstance(data, dict)
        ]) / max(1, total_topics)
        
        return f"""**Progress Summary:**
- Total Topics: {total_topics}
- Completed Topics: {completed_topics}
- Average Completion: {avg_completion:.1f}%
- Completion Rate: {(completed_topics/max(1, total_topics)*100):.1f}%"""
    
    async def _arun(self, user_id: str, action: str, data: Optional[Dict[str, Any]] = None) -> str:
        """Async version of progress management."""
        return self._run(user_id, action, data)


class ExerciseGeneratorTool(BaseTool):
    """Tool for generating learning exercises."""

    name: str = "exercise_generator"
    description: str = "Generate practice exercises for learning topics"
    args_schema: Type[BaseModel] = ExerciseGeneratorInput
    
    def __init__(self):
        super().__init__()
        self.exercise_templates = self._load_exercise_templates()
    
    def _load_exercise_templates(self) -> Dict[str, Dict[str, List[str]]]:
        """Load exercise templates."""
        return {
            "langchain": {
                "coding": [
                    "Create a simple LangChain application that uses a prompt template to generate {topic} explanations",
                    "Build a chain that processes user input about {topic} and provides structured responses",
                    "Implement a memory system that remembers previous {topic} discussions",
                    "Create an agent that can answer questions about {topic} using external tools"
                ],
                "conceptual": [
                    "Explain how {topic} works in the context of LangChain applications",
                    "Compare and contrast different approaches to implementing {topic} in LangChain",
                    "Describe the benefits and limitations of using {topic} in production applications",
                    "Design a system architecture that incorporates {topic} for a real-world use case"
                ],
                "practical": [
                    "Set up a development environment for working with {topic} in LangChain",
                    "Debug a common issue that occurs when implementing {topic}",
                    "Optimize the performance of a {topic} implementation",
                    "Deploy a {topic} application to a cloud platform"
                ]
            },
            "langgraph": {
                "coding": [
                    "Create a StateGraph that manages {topic} workflow",
                    "Implement conditional edges based on {topic} criteria",
                    "Build a multi-agent system that collaborates on {topic} tasks",
                    "Add checkpointing to persist {topic} state across sessions"
                ],
                "conceptual": [
                    "Explain how state management works in {topic} workflows",
                    "Describe the advantages of using LangGraph for {topic} applications",
                    "Compare LangGraph workflows to traditional chain approaches for {topic}",
                    "Design a complex {topic} workflow with multiple decision points"
                ]
            }
        }
    
    def _run(self, topic: str, exercise_type: str = "coding", difficulty: str = "intermediate", framework: str = "langchain") -> str:
        """Generate an exercise."""
        try:
            framework_lower = framework.lower()
            
            if framework_lower not in self.exercise_templates:
                return f"No exercise templates available for framework: {framework}"
            
            if exercise_type not in self.exercise_templates[framework_lower]:
                return f"No {exercise_type} exercises available for {framework}"
            
            templates = self.exercise_templates[framework_lower][exercise_type]
            
            # Select random template
            template = random.choice(templates)
            
            # Generate exercise
            exercise = template.format(topic=topic)
            
            # Add difficulty-specific guidance
            guidance = self._get_difficulty_guidance(difficulty, exercise_type)
            
            result = [
                f"**{exercise_type.title()} Exercise: {topic.title()}**",
                f"**Difficulty:** {difficulty.title()}",
                "",
                f"**Task:** {exercise}",
                "",
                f"**Guidance:** {guidance}",
                "",
                "**Success Criteria:**",
                "- Code runs without errors (for coding exercises)",
                "- Demonstrates understanding of key concepts",
                "- Follows best practices and conventions",
                "- Includes appropriate error handling"
            ]
            
            return "\n".join(result)
        
        except Exception as e:
            return f"Error generating exercise: {str(e)}"
    
    def _get_difficulty_guidance(self, difficulty: str, exercise_type: str) -> str:
        """Get guidance based on difficulty level."""
        guidance_map = {
            "beginner": {
                "coding": "Start with the basic imports and simple examples. Focus on getting something working first, then add complexity.",
                "conceptual": "Focus on the main concepts and basic definitions. Use simple examples to illustrate your points.",
                "practical": "Follow step-by-step instructions carefully. Don't worry about optimization at this stage."
            },
            "intermediate": {
                "coding": "Include error handling and consider edge cases. Add comments explaining your approach.",
                "conceptual": "Provide detailed explanations with examples. Compare different approaches where relevant.",
                "practical": "Consider performance and scalability. Think about real-world constraints and requirements."
            },
            "advanced": {
                "coding": "Implement advanced features and optimizations. Consider extensibility and maintainability.",
                "conceptual": "Provide deep analysis with multiple perspectives. Consider trade-offs and alternative approaches.",
                "practical": "Design for production use. Include monitoring, logging, and deployment considerations."
            }
        }
        
        return guidance_map.get(difficulty, {}).get(exercise_type, "Apply best practices for your skill level.")
    
    async def _arun(self, topic: str, exercise_type: str = "coding", difficulty: str = "intermediate", framework: str = "langchain") -> str:
        """Async version of exercise generation."""
        return self._run(topic, exercise_type, difficulty, framework)
