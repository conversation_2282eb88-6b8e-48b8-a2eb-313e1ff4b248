# GAAPF Quick Start Guide

Get up and running with <PERSON><PERSON><PERSON> (Guidance AI Agent for Python Framework) in minutes!

## 🚀 Quick Setup

### 1. <PERSON><PERSON> and <PERSON>stall

```bash
# Clone the repository
git clone https://github.com/your-username/gaapf-guidance-ai-agent.git
cd gaapf-guidance-ai-agent

# Create virtual environment
python -m venv gaapf-env
source gaapf-env/bin/activate  # On Windows: gaapf-env\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### 2. Configure API Keys

```bash
# Copy environment template
cp env.example .env

# Edit .env and add at least one API key:
# GOOGLE_API_KEY=your_google_gemini_key
# OPENAI_API_KEY=your_openai_key  
# ANTHROPIC_API_KEY=your_anthropic_key
```

### 3. Test Installation

```bash
# Test basic functionality
python examples/basic_usage.py

# Or start the CLI
python -m pyframeworks_assistant.interfaces.cli.main
```

## 🎯 5-Minute Demo

### Option 1: CLI Interface

```bash
# Start the interactive CLI
gaapf-cli

# Follow the prompts to:
# 1. Create your user profile
# 2. Select a framework (LangChain/LangGraph)
# 3. Choose a learning module
# 4. Start chatting with AI agents!
```

### Option 2: Streamlit Web Demo

```bash
# Launch web interface
streamlit run src/pyframeworks_assistant/interfaces/streamlit_demo.py

# Open browser to http://localhost:8501
# Create profile and start learning!
```

### Option 3: Python API

```python
import asyncio
from pyframeworks_assistant import *

async def quick_demo():
    # Setup
    from pyframeworks_assistant.interfaces.cli.llm_setup import setup_llm
    llm = setup_llm()
    hub = LearningHubCore(llm)
    await hub.initialize()
    
    # Create profile
    profile = UserProfile(
        user_id="demo_user",
        name="Demo User",
        python_skill_level=SkillLevel.INTERMEDIATE,
        learning_goals=["Learn LangChain"]
    )
    
    # Start session
    session_id, info = await hub.start_learning_session(
        user_profile=profile,
        framework=SupportedFrameworks.LANGCHAIN,
        module_id="lc_basics"
    )
    
    # Ask question
    result = await hub.process_user_message(
        session_id=session_id,
        user_message="What is LangChain?"
    )
    
    print(f"Agent: {result['agent_role']}")
    print(f"Response: {result['response']}")

# Run demo
asyncio.run(quick_demo())
```

## 🤖 Understanding GAAPF

### What Makes GAAPF Special?

1. **12 Specialized AI Agents** - Each expert in different aspects of learning
2. **Adaptive Constellations** - Agent teams that adapt to your learning style
3. **Temporal Optimization** - System learns from your patterns and improves
4. **Multi-Framework Support** - LangChain, LangGraph, and more coming

### The 12 Agents

**Knowledge Domain:**
- 👨‍🏫 **Instructor** - Structured teaching and explanations
- 📚 **Documentation Expert** - Framework docs and references  
- 🔍 **Research Assistant** - Latest info and resources
- 🧠 **Knowledge Synthesizer** - Concept integration

**Practice Domain:**
- 💻 **Code Assistant** - Implementation guidance
- 🏋️ **Practice Facilitator** - Hands-on exercises
- 🏗️ **Project Guide** - End-to-end projects
- 🔧 **Troubleshooter** - Debugging help

**Support Domain:**
- 🎯 **Mentor** - Learning guidance
- 💪 **Motivational Coach** - Inspiration and momentum

**Assessment Domain:**
- 📊 **Assessment Agent** - Knowledge evaluation
- 📈 **Progress Tracker** - Learning analytics

### Learning Constellations

GAAPF automatically selects the best agent team for you:

- **Knowledge Intensive** (80% theory, 20% practice) - Deep understanding
- **Hands-On Focused** (20% theory, 80% practice) - Learn by doing
- **Theory-Practice Balanced** (50/50) - Balanced approach
- **Basic Learning** - Gentle introduction for beginners
- **Guided Learning** - Structured mentorship

## 📚 Learning Paths

### LangChain Track

1. **LangChain Basics** - Core concepts and components
2. **Chains and Composition** - Building processing pipelines
3. **Memory Systems** - Managing conversation context
4. **Agents and Tools** - Intelligent agents with capabilities
5. **Advanced Patterns** - Production-ready applications

### LangGraph Track

1. **LangGraph Fundamentals** - Stateful workflows
2. **Multi-Agent Systems** - Coordinated agent teams
3. **State Management** - Complex state handling
4. **Advanced Workflows** - Production deployments

## 🛠️ Customization

### Custom User Profile

```python
profile = UserProfile(
    user_id="your_id",
    name="Your Name",
    programming_experience_years=5,
    python_skill_level=SkillLevel.ADVANCED,
    ai_ml_experience=SkillLevel.INTERMEDIATE,
    preferred_learning_style=LearningStyle.HANDS_ON,
    learning_pace=LearningPace.FAST,
    learning_goals=[
        "Build production RAG systems",
        "Master agent architectures",
        "Learn advanced LangGraph patterns"
    ]
)
```

### Environment Configuration

```bash
# LLM Providers (choose at least one)
GOOGLE_API_KEY=your_google_key
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key

# Optional: Enhanced search
TAVILY_API_KEY=your_tavily_key

# Optional: LangSmith tracing
LANGCHAIN_TRACING_V2=true
LANGCHAIN_API_KEY=your_langsmith_key

# System tuning
GAAPF_MAX_CONCURRENT_AGENTS=16
GAAPF_CONSTELLATION_TIMEOUT=300
GAAPF_LOG_LEVEL=INFO
```

## 🔧 Troubleshooting

### Common Issues

**"No LLM provider available"**
```bash
# Check API keys are set
python -c "from pyframeworks_assistant.config.system_config import system_config; print('Has LLM:', system_config.has_llm_api_key())"
```

**Import errors**
```bash
# Reinstall in development mode
pip install -e .
```

**Slow responses**
```bash
# Try different LLM provider or check network
# Google Gemini is usually fastest
```

### Getting Help

- 📖 Read the full [README.md](README.md)
- 🔧 Check [INSTALLATION.md](INSTALLATION.md) for detailed setup
- 🐛 Open an issue on GitHub
- 💬 Join our community discussions

## 🎯 Next Steps

1. **Complete the basic tutorial** - Run through all examples
2. **Try different learning styles** - Experiment with constellations
3. **Build a real project** - Apply what you learn
4. **Contribute back** - Share improvements and feedback

## 📈 Learning Tips

- **Be specific** - Ask detailed questions for better responses
- **Request examples** - Agents love showing code examples
- **Ask for exercises** - Practice makes perfect
- **Use handoffs** - Let agents guide you to specialists
- **Track progress** - Review your analytics regularly

---

**Ready to start your AI framework learning journey?** 🚀

```bash
gaapf-cli
```

*Happy learning with GAAPF!*
