"""Analytics engine for learning metrics and insights."""

import json
import asyncio
from typing import Dict, List, Optional, Any, Tu<PERSON>
from datetime import datetime, timedelta
from pathlib import Path
from pydantic import BaseModel, Field
import numpy as np
from collections import defaultdict

from ..config.user_profiles import UserProfile
from ..config.framework_configs import SupportedFrameworks
from .constellation_types import ConstellationType


class LearningMetrics(BaseModel):
    """Comprehensive learning metrics for analysis."""
    
    user_id: str
    timestamp: datetime
    
    # Session metrics
    session_duration_minutes: float
    interactions_count: int
    constellation_type: ConstellationType
    framework: SupportedFrameworks
    module_id: str
    
    # Performance metrics
    comprehension_score: float = Field(0.0, ge=0.0, le=1.0)
    engagement_score: float = Field(0.0, ge=0.0, le=1.0)
    efficiency_score: float = Field(0.0, ge=0.0, le=1.0)
    satisfaction_score: float = Field(0.0, ge=0.0, le=1.0)
    
    # Behavioral metrics
    question_complexity: float = Field(0.0, ge=0.0, le=1.0)
    help_seeking_frequency: float = Field(0.0, ge=0.0, le=1.0)
    persistence_score: float = Field(0.0, ge=0.0, le=1.0)
    
    # Learning outcomes
    concepts_learned: List[str] = Field(default_factory=list)
    skills_practiced: List[str] = Field(default_factory=list)
    completion_rate: float = Field(0.0, ge=0.0, le=1.0)


class LearningInsight(BaseModel):
    """Generated insight about learning patterns."""
    
    insight_id: str
    user_id: str
    insight_type: str  # "strength", "weakness", "recommendation", "pattern"
    title: str
    description: str
    confidence: float = Field(0.0, ge=0.0, le=1.0)
    
    # Supporting data
    evidence: List[str] = Field(default_factory=list)
    metrics_used: List[str] = Field(default_factory=list)
    
    # Actionability
    actionable: bool = True
    recommended_actions: List[str] = Field(default_factory=list)
    
    # Metadata
    generated_at: datetime = Field(default_factory=datetime.now)
    relevance_score: float = Field(1.0, ge=0.0, le=1.0)


class AnalyticsEngine:
    """Engine for generating learning analytics and insights."""
    
    def __init__(self, data_dir: str = "analytics_data"):
        """Initialize the analytics engine.
        
        Args:
            data_dir: Directory for storing analytics data
        """
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True)
        
        # Data storage
        self.metrics_history: List[LearningMetrics] = []
        self.insights_cache: Dict[str, List[LearningInsight]] = defaultdict(list)
        
        # Analysis parameters
        self.min_sessions_for_insights = 3
        self.insight_confidence_threshold = 0.6
        
        # Load existing data
        asyncio.create_task(self._load_analytics_data())
    
    async def _load_analytics_data(self) -> None:
        """Load historical analytics data."""
        try:
            # Load metrics
            metrics_file = self.data_dir / "learning_metrics.json"
            if metrics_file.exists():
                with open(metrics_file, 'r') as f:
                    metrics_data = json.load(f)
                    self.metrics_history = [
                        LearningMetrics(**metric) for metric in metrics_data
                    ]
            
            # Load insights
            insights_file = self.data_dir / "learning_insights.json"
            if insights_file.exists():
                with open(insights_file, 'r') as f:
                    insights_data = json.load(f)
                    for user_id, user_insights in insights_data.items():
                        self.insights_cache[user_id] = [
                            LearningInsight(**insight) for insight in user_insights
                        ]
        
        except Exception as e:
            print(f"Warning: Could not load analytics data: {e}")
    
    async def record_learning_metrics(
        self,
        user_profile: UserProfile,
        session_data: Dict[str, Any],
        constellation_type: ConstellationType,
        framework: SupportedFrameworks,
        module_id: str
    ) -> None:
        """Record learning metrics for a completed session.
        
        Args:
            user_profile: User profile
            session_data: Session data and metrics
            constellation_type: Constellation type used
            framework: Framework being learned
            module_id: Module within framework
        """
        # Create metrics record
        metrics = LearningMetrics(
            user_id=user_profile.user_id,
            timestamp=datetime.now(),
            session_duration_minutes=session_data.get('duration_minutes', 0),
            interactions_count=session_data.get('interactions_count', 0),
            constellation_type=constellation_type,
            framework=framework,
            module_id=module_id,
            comprehension_score=session_data.get('comprehension_score', 0.5),
            engagement_score=session_data.get('engagement_score', 0.5),
            efficiency_score=session_data.get('efficiency_score', 0.5),
            satisfaction_score=session_data.get('satisfaction_score', 0.5),
            question_complexity=session_data.get('question_complexity', 0.5),
            help_seeking_frequency=session_data.get('help_seeking_frequency', 0.5),
            persistence_score=session_data.get('persistence_score', 0.5),
            concepts_learned=session_data.get('concepts_learned', []),
            skills_practiced=session_data.get('skills_practiced', []),
            completion_rate=session_data.get('completion_rate', 0.5)
        )
        
        # Store metrics
        self.metrics_history.append(metrics)
        
        # Generate insights if enough data
        user_sessions = [m for m in self.metrics_history if m.user_id == user_profile.user_id]
        if len(user_sessions) >= self.min_sessions_for_insights:
            await self._generate_user_insights(user_profile.user_id)
        
        # Persist data
        await self._persist_analytics_data()
    
    async def _generate_user_insights(self, user_id: str) -> None:
        """Generate insights for a specific user."""
        user_metrics = [m for m in self.metrics_history if m.user_id == user_id]
        
        if len(user_metrics) < self.min_sessions_for_insights:
            return
        
        # Clear old insights
        self.insights_cache[user_id] = []
        
        # Generate different types of insights
        await self._analyze_learning_strengths(user_id, user_metrics)
        await self._analyze_learning_challenges(user_id, user_metrics)
        await self._analyze_constellation_preferences(user_id, user_metrics)
        await self._analyze_learning_patterns(user_id, user_metrics)
        await self._generate_recommendations(user_id, user_metrics)
    
    async def _analyze_learning_strengths(self, user_id: str, metrics: List[LearningMetrics]) -> None:
        """Analyze user's learning strengths."""
        
        # Calculate average scores
        avg_comprehension = np.mean([m.comprehension_score for m in metrics])
        avg_engagement = np.mean([m.engagement_score for m in metrics])
        avg_efficiency = np.mean([m.efficiency_score for m in metrics])
        avg_persistence = np.mean([m.persistence_score for m in metrics])
        
        strengths = []
        
        if avg_comprehension > 0.7:
            strengths.append(("comprehension", avg_comprehension, "Strong conceptual understanding"))
        
        if avg_engagement > 0.7:
            strengths.append(("engagement", avg_engagement, "High engagement and participation"))
        
        if avg_efficiency > 0.7:
            strengths.append(("efficiency", avg_efficiency, "Efficient learning pace"))
        
        if avg_persistence > 0.7:
            strengths.append(("persistence", avg_persistence, "Good persistence through challenges"))
        
        # Create strength insights
        for strength_type, score, description in strengths:
            insight = LearningInsight(
                insight_id=f"{user_id}_strength_{strength_type}",
                user_id=user_id,
                insight_type="strength",
                title=f"Strong {strength_type.title()}",
                description=description,
                confidence=min(0.95, score),
                evidence=[f"Average {strength_type} score: {score:.2f}"],
                metrics_used=[f"{strength_type}_score"],
                recommended_actions=[f"Continue leveraging your {strength_type} strength"]
            )
            self.insights_cache[user_id].append(insight)
    
    async def _analyze_learning_challenges(self, user_id: str, metrics: List[LearningMetrics]) -> None:
        """Analyze user's learning challenges."""
        
        # Calculate average scores
        avg_comprehension = np.mean([m.comprehension_score for m in metrics])
        avg_engagement = np.mean([m.engagement_score for m in metrics])
        avg_efficiency = np.mean([m.efficiency_score for m in metrics])
        avg_completion = np.mean([m.completion_rate for m in metrics])
        
        challenges = []
        
        if avg_comprehension < 0.5:
            challenges.append(("comprehension", avg_comprehension, "Conceptual understanding needs improvement"))
        
        if avg_engagement < 0.5:
            challenges.append(("engagement", avg_engagement, "Low engagement levels"))
        
        if avg_efficiency < 0.5:
            challenges.append(("efficiency", avg_efficiency, "Learning efficiency could be improved"))
        
        if avg_completion < 0.5:
            challenges.append(("completion", avg_completion, "Low task completion rates"))
        
        # Create challenge insights
        for challenge_type, score, description in challenges:
            actions = self._get_improvement_actions(challenge_type)
            
            insight = LearningInsight(
                insight_id=f"{user_id}_challenge_{challenge_type}",
                user_id=user_id,
                insight_type="weakness",
                title=f"Improvement Opportunity: {challenge_type.title()}",
                description=description,
                confidence=1.0 - score,  # Higher confidence for lower scores
                evidence=[f"Average {challenge_type} score: {score:.2f}"],
                metrics_used=[f"{challenge_type}_score"],
                recommended_actions=actions
            )
            self.insights_cache[user_id].append(insight)
    
    def _get_improvement_actions(self, challenge_type: str) -> List[str]:
        """Get recommended actions for specific challenges."""
        actions = {
            "comprehension": [
                "Try the Knowledge Intensive constellation for deeper understanding",
                "Request more detailed explanations from the Instructor agent",
                "Use the Documentation Expert for reference materials"
            ],
            "engagement": [
                "Switch to Hands-On Focused constellation for more interactive learning",
                "Try shorter, more frequent learning sessions",
                "Engage with the Motivational Coach for inspiration"
            ],
            "efficiency": [
                "Use the Guided Learning constellation for structured progression",
                "Set specific learning goals for each session",
                "Focus on one concept at a time"
            ],
            "completion": [
                "Break down tasks into smaller, manageable steps",
                "Use the Practice Facilitator for guided exercises",
                "Set realistic session duration goals"
            ]
        }
        return actions.get(challenge_type, ["Seek guidance from the Mentor agent"])
    
    async def _analyze_constellation_preferences(self, user_id: str, metrics: List[LearningMetrics]) -> None:
        """Analyze user's constellation preferences."""
        
        # Group metrics by constellation type
        constellation_performance = defaultdict(list)
        for metric in metrics:
            overall_score = (
                metric.comprehension_score + metric.engagement_score + 
                metric.efficiency_score + metric.completion_rate
            ) / 4
            constellation_performance[metric.constellation_type].append(overall_score)
        
        # Find best performing constellation
        constellation_averages = {
            const_type: np.mean(scores) 
            for const_type, scores in constellation_performance.items()
            if len(scores) >= 2  # Need at least 2 sessions
        }
        
        if constellation_averages:
            best_constellation = max(constellation_averages.items(), key=lambda x: x[1])
            worst_constellation = min(constellation_averages.items(), key=lambda x: x[1])
            
            # Best constellation insight
            if best_constellation[1] > 0.6:
                insight = LearningInsight(
                    insight_id=f"{user_id}_best_constellation",
                    user_id=user_id,
                    insight_type="pattern",
                    title=f"Optimal Constellation: {best_constellation[0].value.replace('_', ' ').title()}",
                    description=f"You perform best with the {best_constellation[0].value.replace('_', ' ')} constellation",
                    confidence=min(0.9, best_constellation[1]),
                    evidence=[f"Average performance: {best_constellation[1]:.2f}"],
                    recommended_actions=[f"Continue using {best_constellation[0].value.replace('_', ' ')} constellation for optimal learning"]
                )
                self.insights_cache[user_id].append(insight)
            
            # Constellation to avoid insight
            if worst_constellation[1] < 0.4 and len(constellation_averages) > 1:
                insight = LearningInsight(
                    insight_id=f"{user_id}_avoid_constellation",
                    user_id=user_id,
                    insight_type="recommendation",
                    title=f"Consider Alternatives to {worst_constellation[0].value.replace('_', ' ').title()}",
                    description=f"The {worst_constellation[0].value.replace('_', ' ')} constellation shows lower performance",
                    confidence=1.0 - worst_constellation[1],
                    evidence=[f"Average performance: {worst_constellation[1]:.2f}"],
                    recommended_actions=[f"Try {best_constellation[0].value.replace('_', ' ')} constellation instead"]
                )
                self.insights_cache[user_id].append(insight)
    
    async def _analyze_learning_patterns(self, user_id: str, metrics: List[LearningMetrics]) -> None:
        """Analyze temporal learning patterns."""
        
        if len(metrics) < 5:
            return
        
        # Sort by timestamp
        sorted_metrics = sorted(metrics, key=lambda x: x.timestamp)
        
        # Analyze trends
        comprehension_trend = [m.comprehension_score for m in sorted_metrics[-5:]]
        engagement_trend = [m.engagement_score for m in sorted_metrics[-5:]]
        
        # Check for improvement trends
        if len(comprehension_trend) >= 3:
            comprehension_slope = np.polyfit(range(len(comprehension_trend)), comprehension_trend, 1)[0]
            engagement_slope = np.polyfit(range(len(engagement_trend)), engagement_trend, 1)[0]
            
            if comprehension_slope > 0.05:  # Improving trend
                insight = LearningInsight(
                    insight_id=f"{user_id}_improving_comprehension",
                    user_id=user_id,
                    insight_type="pattern",
                    title="Improving Comprehension Trend",
                    description="Your understanding is steadily improving over recent sessions",
                    confidence=min(0.8, comprehension_slope * 10),
                    evidence=[f"Comprehension improvement rate: {comprehension_slope:.3f}"],
                    recommended_actions=["Continue with your current learning approach"]
                )
                self.insights_cache[user_id].append(insight)
            
            if engagement_slope < -0.05:  # Declining trend
                insight = LearningInsight(
                    insight_id=f"{user_id}_declining_engagement",
                    user_id=user_id,
                    insight_type="recommendation",
                    title="Engagement Declining",
                    description="Your engagement levels have been decreasing recently",
                    confidence=min(0.8, abs(engagement_slope) * 10),
                    evidence=[f"Engagement decline rate: {engagement_slope:.3f}"],
                    recommended_actions=[
                        "Try a different constellation type",
                        "Take a short break and return refreshed",
                        "Engage with the Motivational Coach"
                    ]
                )
                self.insights_cache[user_id].append(insight)
    
    async def _generate_recommendations(self, user_id: str, metrics: List[LearningMetrics]) -> None:
        """Generate personalized learning recommendations."""
        
        recent_metrics = sorted(metrics, key=lambda x: x.timestamp)[-3:]
        
        # Session length recommendations
        avg_duration = np.mean([m.session_duration_minutes for m in recent_metrics])
        avg_efficiency = np.mean([m.efficiency_score for m in recent_metrics])
        
        if avg_duration > 60 and avg_efficiency < 0.5:
            insight = LearningInsight(
                insight_id=f"{user_id}_session_length_rec",
                user_id=user_id,
                insight_type="recommendation",
                title="Consider Shorter Sessions",
                description="Shorter learning sessions might improve your efficiency",
                confidence=0.7,
                evidence=[f"Average session: {avg_duration:.1f} minutes, efficiency: {avg_efficiency:.2f}"],
                recommended_actions=["Try 30-45 minute sessions", "Take breaks between topics"]
            )
            self.insights_cache[user_id].append(insight)
        
        # Framework progression recommendations
        frameworks_learned = list(set([m.framework for m in metrics]))
        if len(frameworks_learned) == 1 and len(metrics) > 10:
            insight = LearningInsight(
                insight_id=f"{user_id}_framework_expansion",
                user_id=user_id,
                insight_type="recommendation",
                title="Ready for Framework Expansion",
                description="You've made good progress - consider exploring additional frameworks",
                confidence=0.8,
                evidence=[f"Completed {len(metrics)} sessions in {frameworks_learned[0].value}"],
                recommended_actions=["Explore LangGraph for advanced workflows", "Try building a complete project"]
            )
            self.insights_cache[user_id].append(insight)
    
    def get_user_insights(self, user_id: str, insight_types: Optional[List[str]] = None) -> List[LearningInsight]:
        """Get insights for a specific user.
        
        Args:
            user_id: User identifier
            insight_types: Optional filter for insight types
            
        Returns:
            List of relevant insights
        """
        user_insights = self.insights_cache.get(user_id, [])
        
        if insight_types:
            user_insights = [i for i in user_insights if i.insight_type in insight_types]
        
        # Sort by relevance and confidence
        user_insights.sort(key=lambda x: (x.relevance_score * x.confidence), reverse=True)
        
        return user_insights
    
    def get_learning_dashboard(self, user_id: str) -> Dict[str, Any]:
        """Get comprehensive learning dashboard for user."""
        user_metrics = [m for m in self.metrics_history if m.user_id == user_id]
        user_insights = self.insights_cache.get(user_id, [])
        
        if not user_metrics:
            return {"error": "No learning data found"}
        
        # Calculate summary statistics
        total_sessions = len(user_metrics)
        total_time = sum(m.session_duration_minutes for m in user_metrics)
        avg_comprehension = np.mean([m.comprehension_score for m in user_metrics])
        avg_engagement = np.mean([m.engagement_score for m in user_metrics])
        
        # Recent performance (last 5 sessions)
        recent_metrics = sorted(user_metrics, key=lambda x: x.timestamp)[-5:]
        recent_comprehension = np.mean([m.comprehension_score for m in recent_metrics])
        recent_engagement = np.mean([m.engagement_score for m in recent_metrics])
        
        return {
            "user_id": user_id,
            "summary": {
                "total_sessions": total_sessions,
                "total_learning_time_minutes": total_time,
                "average_comprehension": avg_comprehension,
                "average_engagement": avg_engagement,
                "recent_comprehension": recent_comprehension,
                "recent_engagement": recent_engagement
            },
            "insights": {
                "total_insights": len(user_insights),
                "strengths": len([i for i in user_insights if i.insight_type == "strength"]),
                "challenges": len([i for i in user_insights if i.insight_type == "weakness"]),
                "recommendations": len([i for i in user_insights if i.insight_type == "recommendation"])
            },
            "top_insights": [
                {
                    "title": insight.title,
                    "description": insight.description,
                    "type": insight.insight_type,
                    "confidence": insight.confidence,
                    "actions": insight.recommended_actions[:2]  # Top 2 actions
                }
                for insight in user_insights[:5]  # Top 5 insights
            ]
        }
    
    async def _persist_analytics_data(self) -> None:
        """Persist analytics data to storage."""
        try:
            # Save metrics
            metrics_data = [metric.dict() for metric in self.metrics_history]
            with open(self.data_dir / "learning_metrics.json", 'w') as f:
                json.dump(metrics_data, f, indent=2, default=str)
            
            # Save insights
            insights_data = {}
            for user_id, insights in self.insights_cache.items():
                insights_data[user_id] = [insight.dict() for insight in insights]
            
            with open(self.data_dir / "learning_insights.json", 'w') as f:
                json.dump(insights_data, f, indent=2, default=str)
                
        except Exception as e:
            print(f"Warning: Could not persist analytics data: {e}")
