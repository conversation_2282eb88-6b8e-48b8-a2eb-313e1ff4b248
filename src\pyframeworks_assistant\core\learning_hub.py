"""Learning Hub Core - Central coordination system for GAAPF."""

import asyncio
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from langchain_core.language_models import BaseChatModel

from ..config.user_profiles import UserProfile
from ..config.framework_configs import SupportedFrameworks, get_framework_config
from .constellation_types import ConstellationType
from .constellation import ConstellationManager
from .temporal_state import TemporalStateManager
from .knowledge_graph import KnowledgeGraphManager
from .analytics_engine import AnalyticsEngine
from ..memory.memory_manager import MemoryManager
from ..tools.tool_manager import ToolManager


class LearningSession:
    """Represents an active learning session."""
    
    def __init__(
        self,
        session_id: str,
        user_profile: UserProfile,
        framework: SupportedFrameworks,
        module_id: str,
        constellation_type: ConstellationType
    ):
        self.session_id = session_id
        self.user_profile = user_profile
        self.framework = framework
        self.module_id = module_id
        self.constellation_type = constellation_type
        self.start_time = datetime.now()
        self.interactions = []
        self.current_context = {}
        self.is_active = True


class LearningHubCore:
    """Central coordination system for GAAPF learning experiences."""
    
    def __init__(self, llm: BaseChatModel, tools: Optional[Dict[str, List[Any]]] = None):
        """Initialize the Learning Hub Core.
        
        Args:
            llm: Language model for agents
            tools: Optional tools for agents
        """
        self.llm = llm
        
        # Initialize core components
        self.constellation_manager = ConstellationManager(llm, tools)
        self.temporal_manager = TemporalStateManager()
        self.knowledge_graph = KnowledgeGraphManager()
        self.analytics_engine = AnalyticsEngine()
        self.memory_manager = MemoryManager()
        self.tool_manager = ToolManager()
        
        # Active sessions
        self.active_sessions: Dict[str, LearningSession] = {}
        
        # System state
        self.is_initialized = False
    
    async def initialize(self) -> None:
        """Initialize the Learning Hub Core system."""
        if self.is_initialized:
            return
        
        # Initialize components
        await self.temporal_manager._load_historical_data()
        await self.knowledge_graph.initialize()
        await self.memory_manager.initialize()
        
        self.is_initialized = True
    
    async def start_learning_session(
        self,
        user_profile: UserProfile,
        framework: SupportedFrameworks,
        module_id: str,
        session_preferences: Optional[Dict[str, Any]] = None
    ) -> Tuple[str, Dict[str, Any]]:
        """Start a new learning session.
        
        Args:
            user_profile: User profile for personalization
            framework: Framework to learn
            module_id: Specific module within framework
            session_preferences: Optional session preferences
            
        Returns:
            Tuple of (session_id, session_info)
        """
        if not self.is_initialized:
            await self.initialize()
        
        # Generate session ID
        session_id = f"{user_profile.user_id}_{framework.value}_{module_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Get optimal constellation type
        constellation_type, confidence = await self.temporal_manager.optimize_constellation_selection(
            user_profile=user_profile,
            framework=framework,
            module_id=module_id,
            session_context=session_preferences or {}
        )
        
        # Create constellation
        constellation = await self.constellation_manager.create_constellation(
            constellation_type=constellation_type,
            user_profile=user_profile,
            framework=framework,
            module_id=module_id,
            session_id=session_id
        )
        
        # Create learning session
        learning_session = LearningSession(
            session_id=session_id,
            user_profile=user_profile,
            framework=framework,
            module_id=module_id,
            constellation_type=constellation_type
        )
        
        # Store session
        self.active_sessions[session_id] = learning_session
        
        # Initialize session memory
        await self.memory_manager.create_session_memory(session_id, user_profile)
        
        # Get framework and module information
        framework_config = get_framework_config(framework)
        module_config = framework_config.modules.get(module_id)
        
        session_info = {
            "session_id": session_id,
            "constellation_type": constellation_type.value,
            "constellation_confidence": confidence,
            "framework": framework.value,
            "module_id": module_id,
            "module_title": module_config.title if module_config else "Unknown Module",
            "estimated_duration": module_config.estimated_duration_minutes if module_config else 30,
            "learning_objectives": module_config.learning_objectives if module_config else [],
            "user_experience_level": user_profile.get_experience_level(),
            "session_start_time": learning_session.start_time.isoformat()
        }
        
        return session_id, session_info
    
    async def process_user_message(
        self,
        session_id: str,
        user_message: str,
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Process a user message within a learning session.
        
        Args:
            session_id: Active session identifier
            user_message: User's message/question
            context: Optional additional context
            
        Returns:
            Response with agent output and metadata
        """
        if session_id not in self.active_sessions:
            return {"error": "Session not found or expired"}
        
        session = self.active_sessions[session_id]
        if not session.is_active:
            return {"error": "Session is no longer active"}
        
        try:
            # Update session context
            if context:
                session.current_context.update(context)
            
            # Store user message in memory
            await self.memory_manager.add_interaction(
                session_id=session_id,
                user_message=user_message,
                timestamp=datetime.now()
            )
            
            # Process with constellation
            result = await self.constellation_manager.run_session(
                session_id=session_id,
                user_message=user_message,
                user_profile=session.user_profile,
                framework=session.framework,
                module_id=session.module_id
            )
            
            # Store agent response in memory
            if "response" in result:
                await self.memory_manager.add_interaction(
                    session_id=session_id,
                    agent_response=result["response"],
                    agent_role=result.get("agent_role"),
                    timestamp=datetime.now()
                )
            
            # Update session tracking
            session.interactions.append({
                "timestamp": datetime.now(),
                "user_message": user_message,
                "agent_response": result.get("response", ""),
                "agent_role": result.get("agent_role"),
                "confidence": result.get("confidence", 0.0)
            })
            
            # Update knowledge graph
            await self.knowledge_graph.update_from_interaction(
                user_id=session.user_profile.user_id,
                framework=session.framework,
                module_id=session.module_id,
                concepts_discussed=self._extract_concepts(user_message, result.get("response", ""))
            )
            
            # Add session metadata to result
            result.update({
                "session_id": session_id,
                "interaction_count": len(session.interactions),
                "session_duration_minutes": (datetime.now() - session.start_time).total_seconds() / 60,
                "constellation_type": session.constellation_type.value
            })
            
            return result
            
        except Exception as e:
            return {"error": f"Error processing message: {str(e)}"}
    
    async def end_learning_session(
        self,
        session_id: str,
        user_feedback: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """End a learning session and generate summary.
        
        Args:
            session_id: Session to end
            user_feedback: Optional user feedback
            
        Returns:
            Session summary and analytics
        """
        if session_id not in self.active_sessions:
            return {"error": "Session not found"}
        
        session = self.active_sessions[session_id]
        session.is_active = False
        
        # Calculate session metrics
        duration_minutes = (datetime.now() - session.start_time).total_seconds() / 60
        interactions_count = len(session.interactions)
        
        # Get constellation summary
        constellation_summary = self.constellation_manager.end_session(session_id)
        
        # Prepare session data for temporal analysis
        session_data = {
            "duration_minutes": duration_minutes,
            "interactions_count": interactions_count,
            "agents_used": constellation_summary.get("agents_used", 0) if constellation_summary else 0,
            "handoff_count": len(constellation_summary.get("handoff_chain", [])) - 1 if constellation_summary else 0,
            "completion_rate": self._estimate_completion_rate(session),
            "user_feedback": user_feedback or {},
            "context": session.current_context
        }
        
        # Record session for temporal optimization
        await self.temporal_manager.record_session(
            session_id=session_id,
            user_profile=session.user_profile,
            constellation_type=session.constellation_type,
            framework=session.framework,
            module_id=session.module_id,
            session_data=session_data
        )
        
        # Update user profile
        session.user_profile.update_learning_time(int(duration_minutes))
        
        # Generate session summary
        summary = {
            "session_id": session_id,
            "duration_minutes": duration_minutes,
            "interactions_count": interactions_count,
            "constellation_type": session.constellation_type.value,
            "framework": session.framework.value,
            "module_id": session.module_id,
            "completion_estimate": session_data["completion_rate"],
            "agents_used": constellation_summary.get("agents_used", 0) if constellation_summary else 0,
            "learning_progress": await self._generate_progress_summary(session),
            "next_recommendations": await self._generate_next_recommendations(session)
        }
        
        # Clean up session
        del self.active_sessions[session_id]
        await self.memory_manager.archive_session_memory(session_id)
        
        return summary
    
    def _extract_concepts(self, user_message: str, agent_response: str) -> List[str]:
        """Extract key concepts from interaction (simplified implementation)."""
        # This would use more sophisticated NLP in practice
        concepts = []
        
        # Simple keyword extraction
        framework_keywords = ["langchain", "langgraph", "chain", "agent", "llm", "prompt", "memory"]
        text = (user_message + " " + agent_response).lower()
        
        for keyword in framework_keywords:
            if keyword in text:
                concepts.append(keyword)
        
        return list(set(concepts))
    
    def _estimate_completion_rate(self, session: LearningSession) -> float:
        """Estimate session completion rate based on interactions."""
        # Simplified completion estimation
        if not session.interactions:
            return 0.0
        
        # Base completion on interaction count and duration
        expected_interactions = max(1, session.start_time.minute // 5)  # Expect interaction every 5 minutes
        actual_interactions = len(session.interactions)
        
        return min(1.0, actual_interactions / expected_interactions)
    
    async def _generate_progress_summary(self, session: LearningSession) -> Dict[str, Any]:
        """Generate learning progress summary for the session."""
        return {
            "concepts_covered": len(self._extract_concepts(
                " ".join([i.get("user_message", "") for i in session.interactions]),
                " ".join([i.get("agent_response", "") for i in session.interactions])
            )),
            "interaction_quality": "high" if len(session.interactions) > 5 else "moderate",
            "engagement_level": "high" if session.interactions else "low"
        }
    
    async def _generate_next_recommendations(self, session: LearningSession) -> List[str]:
        """Generate recommendations for next learning steps."""
        recommendations = []
        
        # Get framework config
        framework_config = get_framework_config(session.framework)
        current_module = framework_config.modules.get(session.module_id)
        
        if current_module:
            # Suggest next modules based on prerequisites
            for module_id, module_config in framework_config.modules.items():
                if session.module_id in module_config.prerequisites:
                    recommendations.append(f"Continue with {module_config.title}")
        
        if not recommendations:
            recommendations.append("Practice with hands-on exercises")
            recommendations.append("Explore advanced topics in the same framework")
        
        return recommendations[:3]  # Return top 3 recommendations
    
    def get_session_status(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get current status of a learning session."""
        if session_id not in self.active_sessions:
            return None
        
        session = self.active_sessions[session_id]
        constellation_status = self.constellation_manager.get_constellation_status(session_id)
        
        return {
            "session_id": session_id,
            "is_active": session.is_active,
            "duration_minutes": (datetime.now() - session.start_time).total_seconds() / 60,
            "interactions_count": len(session.interactions),
            "constellation_status": constellation_status,
            "framework": session.framework.value,
            "module_id": session.module_id
        }
    
    def get_active_sessions(self) -> List[str]:
        """Get list of active session IDs."""
        return [sid for sid, session in self.active_sessions.items() if session.is_active]
