"""Profile management for CLI interface."""

import json
from typing import List, Optional
from pathlib import Path
from rich.console import Console

from ...config.user_profiles import UserProfile
from ...config.system_config import system_config

console = Console()


class ProfileManager:
    """Manages user profiles for the CLI interface."""
    
    def __init__(self):
        """Initialize the profile manager."""
        self.profiles_dir = Path(system_config.user_profiles_dir)
        self.profiles_dir.mkdir(exist_ok=True)
    
    def save_profile(self, profile: UserProfile) -> bool:
        """Save a user profile to disk.
        
        Args:
            profile: User profile to save
            
        Returns:
            True if saved successfully
        """
        try:
            profile_file = self.profiles_dir / f"{profile.user_id}.json"
            profile.save_to_file(profile_file)
            console.print(f"[green]✅ Profile saved to {profile_file}[/green]")
            return True
        
        except Exception as e:
            console.print(f"[red]❌ Failed to save profile: {e}[/red]")
            return False
    
    def load_profile(self, user_id: str) -> Optional[UserProfile]:
        """Load a user profile from disk.
        
        Args:
            user_id: User identifier
            
        Returns:
            User profile or None if not found
        """
        try:
            profile_file = self.profiles_dir / f"{user_id}.json"
            
            if not profile_file.exists():
                console.print(f"[yellow]⚠️ Profile not found: {user_id}[/yellow]")
                return None
            
            profile = UserProfile.load_from_file(profile_file)
            return profile
        
        except Exception as e:
            console.print(f"[red]❌ Failed to load profile: {e}[/red]")
            return None
    
    def list_users(self) -> List[str]:
        """List all available user profiles.
        
        Returns:
            List of user IDs
        """
        try:
            profile_files = list(self.profiles_dir.glob("*.json"))
            user_ids = [f.stem for f in profile_files]
            return sorted(user_ids)
        
        except Exception as e:
            console.print(f"[red]❌ Failed to list users: {e}[/red]")
            return []
    
    def delete_profile(self, user_id: str) -> bool:
        """Delete a user profile.
        
        Args:
            user_id: User identifier
            
        Returns:
            True if deleted successfully
        """
        try:
            profile_file = self.profiles_dir / f"{user_id}.json"
            
            if not profile_file.exists():
                console.print(f"[yellow]⚠️ Profile not found: {user_id}[/yellow]")
                return False
            
            profile_file.unlink()
            console.print(f"[green]✅ Profile deleted: {user_id}[/green]")
            return True
        
        except Exception as e:
            console.print(f"[red]❌ Failed to delete profile: {e}[/red]")
            return False
    
    def update_profile(self, profile: UserProfile) -> bool:
        """Update an existing user profile.
        
        Args:
            profile: Updated user profile
            
        Returns:
            True if updated successfully
        """
        return self.save_profile(profile)
    
    def get_profile_summary(self, user_id: str) -> Optional[dict]:
        """Get a summary of a user profile.
        
        Args:
            user_id: User identifier
            
        Returns:
            Profile summary or None if not found
        """
        profile = self.load_profile(user_id)
        if not profile:
            return None
        
        return {
            "user_id": profile.user_id,
            "name": profile.name,
            "experience_level": profile.get_experience_level(),
            "python_skill": profile.python_skill_level.value,
            "ai_experience": profile.ai_ml_experience.value,
            "learning_style": profile.preferred_learning_style.value,
            "learning_pace": profile.learning_pace.value,
            "total_learning_time": profile.total_learning_time_minutes,
            "completed_modules": len(profile.completed_modules),
            "current_streak": profile.current_streak_days,
            "created_at": profile.created_at.isoformat(),
            "last_updated": profile.last_updated.isoformat()
        }
