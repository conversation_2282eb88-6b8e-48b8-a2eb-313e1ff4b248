"""Tavily search and discovery tools for GAAPF."""

from typing import Dict, List, Optional, Any, Type
from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field
import requests
import json

# Try to import LangChain's built-in Tavily tool, fall back to custom implementation
try:
    from langchain_community.tools.tavily_search import TavilySearchResults
    LANGCHAIN_TAVILY_AVAILABLE = True
except ImportError:
    LANGCHAIN_TAVILY_AVAILABLE = False


class TavilySearchInput(BaseModel):
    """Input for Tavily search tool."""
    query: str = Field(..., description="Search query")
    max_results: int = Field(5, description="Maximum number of results")
    include_domains: Optional[List[str]] = Field(None, description="Domains to include")
    exclude_domains: Optional[List[str]] = Field(None, description="Domains to exclude")


class TavilyExtractInput(BaseModel):
    """Input for Tavily extract tool."""
    url: str = Field(..., description="URL to extract content from")


class TavilyCrawlInput(BaseModel):
    """Input for Tavily crawl tool."""
    url: str = Field(..., description="URL to crawl")
    max_depth: int = Field(1, description="Maximum crawl depth")


class TavilySearchTool(BaseTool):
    """Tool for searching the web using Tavily API."""

    name: str = "tavily_search"
    description: str = "Search the web for information about AI frameworks, documentation, and tutorials"
    args_schema: Type[BaseModel] = TavilySearchInput
    api_key: str = ""
    base_url: str = "https://api.tavily.com"

    def __init__(self, api_key: str, **kwargs):
        super().__init__(api_key=api_key, **kwargs)
    
    def _run(self, query: str, max_results: int = 5, include_domains: Optional[List[str]] = None, exclude_domains: Optional[List[str]] = None) -> str:
        """Execute the search."""
        try:
            # Prepare search request
            payload = {
                "api_key": self.api_key,
                "query": query,
                "max_results": max_results,
                "search_depth": "advanced",
                "include_answer": True,
                "include_raw_content": False
            }
            
            if include_domains:
                payload["include_domains"] = include_domains
            
            if exclude_domains:
                payload["exclude_domains"] = exclude_domains
            
            # Make API request
            response = requests.post(
                f"{self.base_url}/search",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code != 200:
                return f"Search failed with status {response.status_code}: {response.text}"
            
            data = response.json()
            
            # Format results
            results = []
            
            # Include answer if available
            if data.get("answer"):
                results.append(f"**Answer:** {data['answer']}")
            
            # Include search results
            if data.get("results"):
                results.append("**Search Results:**")
                for i, result in enumerate(data["results"][:max_results], 1):
                    title = result.get("title", "No title")
                    url = result.get("url", "")
                    content = result.get("content", "No content available")
                    
                    results.append(f"{i}. **{title}**")
                    results.append(f"   URL: {url}")
                    results.append(f"   Content: {content[:300]}...")
                    results.append("")
            
            return "\n".join(results)
        
        except Exception as e:
            return f"Search error: {str(e)}"
    
    async def _arun(self, query: str, max_results: int = 5, include_domains: Optional[List[str]] = None, exclude_domains: Optional[List[str]] = None) -> str:
        """Async version of search."""
        return self._run(query, max_results, include_domains, exclude_domains)


class TavilyExtractTool(BaseTool):
    """Tool for extracting content from URLs using Tavily API."""

    name: str = "tavily_extract"
    description: str = "Extract and summarize content from a specific URL"
    args_schema: Type[BaseModel] = TavilyExtractInput
    api_key: str = ""
    base_url: str = "https://api.tavily.com"

    def __init__(self, api_key: str, **kwargs):
        super().__init__(api_key=api_key, **kwargs)
    
    def _run(self, url: str) -> str:
        """Extract content from URL."""
        try:
            payload = {
                "api_key": self.api_key,
                "url": url
            }
            
            response = requests.post(
                f"{self.base_url}/extract",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code != 200:
                return f"Extraction failed with status {response.status_code}: {response.text}"
            
            data = response.json()
            
            # Format extracted content
            results = []
            
            if data.get("title"):
                results.append(f"**Title:** {data['title']}")
            
            if data.get("content"):
                results.append(f"**Content:**\n{data['content']}")
            
            if data.get("metadata"):
                metadata = data["metadata"]
                if metadata.get("description"):
                    results.append(f"**Description:** {metadata['description']}")
                
                if metadata.get("keywords"):
                    results.append(f"**Keywords:** {', '.join(metadata['keywords'])}")
            
            return "\n\n".join(results) if results else "No content extracted"
        
        except Exception as e:
            return f"Extraction error: {str(e)}"
    
    async def _arun(self, url: str) -> str:
        """Async version of extract."""
        return self._run(url)


class TavilyCrawlTool(BaseTool):
    """Tool for crawling websites using Tavily API."""

    name: str = "tavily_crawl"
    description: str = "Crawl a website to discover related pages and content"
    args_schema: Type[BaseModel] = TavilyCrawlInput
    api_key: str = ""
    base_url: str = "https://api.tavily.com"

    def __init__(self, api_key: str, **kwargs):
        super().__init__(api_key=api_key, **kwargs)
    
    def _run(self, url: str, max_depth: int = 1) -> str:
        """Crawl website."""
        try:
            payload = {
                "api_key": self.api_key,
                "url": url,
                "max_depth": max_depth,
                "max_pages": 10  # Limit to prevent excessive crawling
            }
            
            response = requests.post(
                f"{self.base_url}/crawl",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code != 200:
                return f"Crawl failed with status {response.status_code}: {response.text}"
            
            data = response.json()
            
            # Format crawl results
            results = []
            results.append(f"**Crawled from:** {url}")
            
            if data.get("pages"):
                results.append(f"**Found {len(data['pages'])} pages:**")
                
                for i, page in enumerate(data["pages"], 1):
                    page_url = page.get("url", "")
                    page_title = page.get("title", "No title")
                    page_content = page.get("content", "")
                    
                    results.append(f"{i}. **{page_title}**")
                    results.append(f"   URL: {page_url}")
                    
                    if page_content:
                        results.append(f"   Preview: {page_content[:200]}...")
                    
                    results.append("")
            
            if data.get("links"):
                results.append(f"**Additional links found:** {len(data['links'])}")
                for link in data["links"][:5]:  # Show first 5 links
                    results.append(f"- {link}")
            
            return "\n".join(results)

        except Exception as e:
            return f"Crawl error: {str(e)}"

    async def _arun(self, url: str, max_depth: int = 1) -> str:
        """Async version of crawl."""
        return self._run(url, max_depth)


def create_tavily_search_tool(api_key: str) -> BaseTool:
    """Create a Tavily search tool using the best available implementation.

    Args:
        api_key: Tavily API key

    Returns:
        BaseTool: Either LangChain's built-in TavilySearchResults or custom TavilySearchTool
    """
    if LANGCHAIN_TAVILY_AVAILABLE:
        # Use LangChain's built-in tool (preferred for LangChain 0.3.x)
        return TavilySearchResults(api_key=api_key, max_results=5)
    else:
        # Fall back to custom implementation
        return TavilySearchTool(api_key=api_key)


def get_tavily_tools(api_key: str) -> List[BaseTool]:
    """Get all available Tavily tools.

    Args:
        api_key: Tavily API key

    Returns:
        List[BaseTool]: List of available Tavily tools
    """
    tools = []

    # Add search tool (prefer LangChain built-in)
    tools.append(create_tavily_search_tool(api_key))

    # Add custom extract and crawl tools
    tools.extend([
        TavilyExtractTool(api_key=api_key),
        TavilyCrawlTool(api_key=api_key)
    ])

    return tools
