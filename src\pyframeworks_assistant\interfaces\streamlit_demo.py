"""Streamlit demo interface for GAAPF system."""

import asyncio
import streamlit as st
from typing import Optional, Dict, Any
import pandas as pd
from datetime import datetime

from ..config.system_config import system_config
from ..config.user_profiles import UserProfile, SkillLevel, LearningPace, LearningStyle
from ..config.framework_configs import SupportedFrameworks, get_framework_config
from ..core.learning_hub import LearningHubCore
from .cli.llm_setup import setup_llm


# Page configuration
st.set_page_config(
    page_title="GAAPF - AI Framework Learning",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)


@st.cache_resource
def initialize_system():
    """Initialize the GAAPF system."""
    try:
        llm = setup_llm()
        if not llm:
            return None, "No LLM provider available. Please check your API keys."
        
        learning_hub = LearningHubCore(llm)
        return learning_hub, None
    except Exception as e:
        return None, f"Failed to initialize system: {e}"


def create_user_profile() -> Optional[UserProfile]:
    """Create user profile from sidebar inputs."""
    with st.sidebar:
        st.header("👤 User Profile")
        
        name = st.text_input("Name", value="Demo User")
        user_id = st.text_input("User ID", value="demo_user")
        
        st.subheader("Experience")
        programming_years = st.slider("Programming Experience (years)", 0, 20, 3)
        python_skill = st.selectbox(
            "Python Skill Level",
            options=["none", "beginner", "intermediate", "advanced", "expert"],
            index=2
        )
        ai_experience = st.selectbox(
            "AI/ML Experience",
            options=["none", "beginner", "intermediate", "advanced", "expert"],
            index=1
        )
        
        st.subheader("Learning Preferences")
        learning_style = st.selectbox(
            "Learning Style",
            options=["visual", "hands_on", "theoretical", "mixed"],
            index=3
        )
        learning_pace = st.selectbox(
            "Learning Pace",
            options=["slow", "moderate", "fast", "intensive"],
            index=1
        )
        
        goals_text = st.text_area(
            "Learning Goals (one per line)",
            value="Learn LangChain basics\nBuild RAG applications\nUnderstand agent workflows"
        )
        goals = [goal.strip() for goal in goals_text.split('\n') if goal.strip()]
        
        if st.button("Create Profile"):
            try:
                profile = UserProfile(
                    user_id=user_id,
                    name=name,
                    programming_experience_years=programming_years,
                    python_skill_level=SkillLevel(python_skill),
                    ai_ml_experience=SkillLevel(ai_experience),
                    preferred_learning_style=LearningStyle(learning_style),
                    learning_pace=LearningPace(learning_pace),
                    learning_goals=goals
                )
                st.session_state.user_profile = profile
                st.success("Profile created!")
                st.rerun()
            except Exception as e:
                st.error(f"Error creating profile: {e}")
        
        return st.session_state.get('user_profile')


def display_framework_selection():
    """Display framework and module selection."""
    st.header("🔧 Framework Selection")
    
    col1, col2 = st.columns(2)
    
    with col1:
        framework_choice = st.selectbox(
            "Select Framework",
            options=["langchain", "langgraph"],
            format_func=lambda x: {
                "langchain": "LangChain - LLM Application Framework",
                "langgraph": "LangGraph - Stateful Multi-Agent Workflows"
            }[x]
        )
        
        framework = SupportedFrameworks(framework_choice)
        framework_config = get_framework_config(framework)
        
        st.info(f"**{framework_config.name}**\n\n{framework_config.description}")
    
    with col2:
        modules = list(framework_config.modules.items())
        module_options = [f"{module_id}: {config.title}" for module_id, config in modules]
        
        selected_module = st.selectbox("Select Module", options=module_options)
        module_id = selected_module.split(":")[0]
        
        module_config = framework_config.modules[module_id]
        
        st.info(f"""
        **{module_config.title}**
        
        {module_config.description}
        
        - **Difficulty:** {module_config.difficulty_level}/5
        - **Duration:** {module_config.estimated_duration_minutes} minutes
        - **Prerequisites:** {', '.join(module_config.prerequisites) if module_config.prerequisites else 'None'}
        """)
    
    return framework, module_id


async def start_learning_session(learning_hub, user_profile, framework, module_id):
    """Start a learning session."""
    try:
        session_id, session_info = await learning_hub.start_learning_session(
            user_profile=user_profile,
            framework=framework,
            module_id=module_id
        )
        return session_id, session_info
    except Exception as e:
        st.error(f"Failed to start session: {e}")
        return None, None


def display_session_info(session_info):
    """Display session information."""
    if not session_info:
        return
    
    st.header("📊 Learning Session")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("Constellation Type", session_info["constellation_type"].replace("_", " ").title())
        st.metric("Framework", session_info["framework"].title())
    
    with col2:
        st.metric("Module", session_info["module_title"])
        st.metric("Experience Level", session_info["user_experience_level"].title())
    
    with col3:
        st.metric("Estimated Duration", f"{session_info['estimated_duration']} min")
        st.metric("Constellation Confidence", f"{session_info.get('constellation_confidence', 0):.1%}")


async def process_user_message(learning_hub, session_id, message):
    """Process user message and get agent response."""
    try:
        result = await learning_hub.process_user_message(
            session_id=session_id,
            user_message=message
        )
        return result
    except Exception as e:
        return {"error": str(e)}


def display_chat_interface(learning_hub, session_id):
    """Display the chat interface."""
    st.header("💬 Learning Chat")
    
    # Initialize chat history
    if "messages" not in st.session_state:
        st.session_state.messages = []
    
    # Display chat messages
    for message in st.session_state.messages:
        with st.chat_message(message["role"]):
            st.markdown(message["content"])
            if message.get("metadata"):
                with st.expander("Details"):
                    st.json(message["metadata"])
    
    # Chat input
    if prompt := st.chat_input("Ask a question about the framework..."):
        # Add user message to chat history
        st.session_state.messages.append({"role": "user", "content": prompt})
        
        # Display user message
        with st.chat_message("user"):
            st.markdown(prompt)
        
        # Get agent response
        with st.chat_message("assistant"):
            with st.spinner("Processing..."):
                result = asyncio.run(process_user_message(learning_hub, session_id, prompt))
            
            if "error" in result:
                st.error(f"Error: {result['error']}")
            else:
                response = result.get("response", "No response")
                agent_role = result.get("agent_role", "assistant")
                confidence = result.get("confidence", 0.0)
                
                # Display response
                st.markdown(response)
                
                # Display metadata
                metadata = {
                    "agent": agent_role.replace("_", " ").title(),
                    "confidence": f"{confidence:.1%}",
                    "session_stats": result.get("session_stats", {})
                }
                
                if result.get("handoff_suggestion"):
                    metadata["next_agent_suggestion"] = result["handoff_suggestion"].replace("_", " ").title()
                
                # Add assistant message to chat history
                st.session_state.messages.append({
                    "role": "assistant",
                    "content": response,
                    "metadata": metadata
                })


def display_analytics_dashboard():
    """Display analytics dashboard."""
    st.header("📈 Learning Analytics")
    
    # Mock analytics data for demo
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Total Sessions", "12", delta="2")
    
    with col2:
        st.metric("Learning Time", "8.5 hrs", delta="1.2 hrs")
    
    with col3:
        st.metric("Concepts Learned", "24", delta="5")
    
    with col4:
        st.metric("Average Satisfaction", "4.2/5", delta="0.3")
    
    # Mock charts
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("Learning Progress")
        progress_data = pd.DataFrame({
            'Date': pd.date_range('2024-01-01', periods=10, freq='D'),
            'Concepts Learned': [2, 3, 1, 4, 2, 3, 5, 2, 3, 4],
            'Session Duration': [30, 45, 20, 60, 35, 40, 75, 25, 50, 55]
        })
        st.line_chart(progress_data.set_index('Date'))
    
    with col2:
        st.subheader("Constellation Effectiveness")
        constellation_data = pd.DataFrame({
            'Constellation': ['Knowledge Intensive', 'Hands-On Focused', 'Theory-Practice Balanced'],
            'Effectiveness': [0.85, 0.92, 0.78]
        })
        st.bar_chart(constellation_data.set_index('Constellation'))


def main():
    """Main Streamlit application."""
    st.title("🤖 GAAPF - Guidance AI Agent for Python Framework")
    st.markdown("*An Adaptive Multi-Agent Learning System for AI Framework Education*")
    
    # Check system configuration
    if not system_config.has_llm_api_key():
        st.error("""
        ❌ **No LLM API keys found!**
        
        Please set at least one of the following environment variables:
        - `GOOGLE_API_KEY` (for Google Gemini)
        - `OPENAI_API_KEY` (for OpenAI GPT)
        - `ANTHROPIC_API_KEY` (for Anthropic Claude)
        
        See `env.example` for configuration details.
        """)
        return
    
    # Initialize system
    learning_hub, error = initialize_system()
    if error:
        st.error(f"❌ {error}")
        return
    
    # Create user profile
    user_profile = create_user_profile()
    if not user_profile:
        st.info("👈 Please create a user profile in the sidebar to get started.")
        return
    
    # Main tabs
    tab1, tab2, tab3 = st.tabs(["🎓 Learning Session", "📊 Analytics", "ℹ️ About"])
    
    with tab1:
        # Framework and module selection
        framework, module_id = display_framework_selection()
        
        if st.button("Start Learning Session", type="primary"):
            with st.spinner("Starting learning session..."):
                session_id, session_info = asyncio.run(
                    start_learning_session(learning_hub, user_profile, framework, module_id)
                )
                
                if session_id:
                    st.session_state.session_id = session_id
                    st.session_state.session_info = session_info
                    st.success("Learning session started!")
                    st.rerun()
        
        # Display session info and chat
        if st.session_state.get('session_id'):
            display_session_info(st.session_state.get('session_info'))
            st.divider()
            display_chat_interface(learning_hub, st.session_state.session_id)
    
    with tab2:
        display_analytics_dashboard()
    
    with tab3:
        st.markdown("""
        ## About GAAPF
        
        **GAAPF (Guidance AI Agent for Python Framework)** is an innovative adaptive learning system that uses multiple specialized AI agents to provide personalized education in AI frameworks like LangChain and LangGraph.
        
        ### Key Features
        
        - **12 Specialized Agents**: Each agent has expertise in different aspects of learning
        - **Adaptive Constellations**: Dynamic agent teams that adapt to your learning style
        - **Temporal Optimization**: The system learns from your progress and optimizes future sessions
        - **Real-time LLM Integration**: Powered by state-of-the-art language models
        - **Comprehensive Analytics**: Track your learning progress and identify areas for improvement
        
        ### Agent Types
        
        **Knowledge Domain:**
        - 👨‍🏫 Instructor - Structured teaching and concept explanation
        - 📚 Documentation Expert - Framework documentation and API references
        - 🔍 Research Assistant - Latest information and community resources
        - 🧠 Knowledge Synthesizer - Concept integration and overview creation
        
        **Practice Domain:**
        - 💻 Code Assistant - Implementation and programming guidance
        - 🏋️ Practice Facilitator - Hands-on exercises and skill development
        - 🏗️ Project Guide - End-to-end project planning and architecture
        - 🔧 Troubleshooter - Debugging and problem-solving
        
        **Support Domain:**
        - 🎯 Mentor - Learning guidance and personal development
        - 💪 Motivational Coach - Inspiration and momentum building
        
        **Assessment Domain:**
        - 📊 Assessment Agent - Knowledge evaluation and feedback
        - 📈 Progress Tracker - Learning analytics and progress monitoring
        
        ### Technology Stack
        
        - **LangChain 0.3.x** - Core agent framework
        - **LangGraph 0.4.x** - Stateful multi-agent workflows
        - **Pydantic 2.x** - Type-safe configuration and validation
        - **Streamlit** - Interactive web interface
        - **Rich** - Enhanced CLI experience
        
        ### Getting Started
        
        1. Set up your LLM API keys (Google Gemini, OpenAI, or Anthropic)
        2. Create your user profile with learning preferences
        3. Select a framework and module to learn
        4. Start your adaptive learning session!
        
        ---
        
        *Built with ❤️ for the AI learning community*
        """)


if __name__ == "__main__":
    main()
