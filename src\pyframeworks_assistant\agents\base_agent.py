"""Base agent class and common functionality for GAAPF agents."""

from abc import ABC, abstractmethod
from enum import Enum
from typing import Dict, List, Optional, Any, Tuple
from pydantic import BaseModel, Field
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage
from langchain_core.language_models import BaseChatModel
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser


class AgentDomain(str, Enum):
    """Agent domain classification."""
    KNOWLEDGE = "knowledge"
    PRACTICE = "practice"
    SUPPORT = "support"
    ASSESSMENT = "assessment"


class AgentRole(str, Enum):
    """Specific agent roles within the constellation."""
    INSTRUCTOR = "instructor"
    DOCUMENTATION_EXPERT = "documentation_expert"
    RESEARCH_ASSISTANT = "research_assistant"
    KNOWLEDGE_SYNTHESIZER = "knowledge_synthesizer"
    CODE_ASSISTANT = "code_assistant"
    PRACTICE_FACILITATOR = "practice_facilitator"
    PROJECT_GUIDE = "project_guide"
    TROUBLESHOOTER = "troubleshooter"
    MENTOR = "mentor"
    MOTIVATIONAL_COACH = "motivational_coach"
    ASSESSMENT = "assessment"
    PROGRESS_TRACKER = "progress_tracker"


class HandoffDecision(BaseModel):
    """Decision about whether to hand off to another agent."""
    should_handoff: bool = Field(..., description="Whether to hand off to another agent")
    next_agent: Optional[str] = Field(None, description="Recommended next agent")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Confidence in the decision")
    reason: str = Field(..., description="Reason for the handoff decision")


class AgentResponse(BaseModel):
    """Response from an agent including content and metadata."""
    content: str = Field(..., description="Agent response content")
    agent_role: AgentRole = Field(..., description="Role of the responding agent")
    handoff_decision: Optional[HandoffDecision] = Field(None, description="Handoff decision")
    tools_used: List[str] = Field(default_factory=list, description="Tools used in response")
    confidence: float = Field(1.0, ge=0.0, le=1.0, description="Confidence in response")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")


class BaseAgent(ABC):
    """Base class for all GAAPF agents."""
    
    def __init__(
        self,
        role: AgentRole,
        domain: AgentDomain,
        llm: BaseChatModel,
        tools: Optional[List[Any]] = None
    ):
        self.role = role
        self.domain = domain
        self.llm = llm
        self.tools = tools or []
        self.conversation_history: List[BaseMessage] = []
        
        # Agent-specific configuration
        self.system_prompt = self._create_system_prompt()
        self.handoff_keywords = self._define_handoff_keywords()
        self.confidence_threshold = 0.7
    
    @abstractmethod
    def _create_system_prompt(self) -> str:
        """Create the system prompt for this agent."""
        pass
    
    @abstractmethod
    def _define_handoff_keywords(self) -> Dict[str, List[str]]:
        """Define keywords that trigger handoffs to other agents."""
        pass
    
    async def process_message(
        self,
        message: str,
        context: Dict[str, Any],
        conversation_history: Optional[List[BaseMessage]] = None
    ) -> AgentResponse:
        """Process a user message and generate a response."""
        
        # Update conversation history
        if conversation_history:
            self.conversation_history = conversation_history
        
        # Add user message to history
        self.conversation_history.append(HumanMessage(content=message))
        
        # Generate response
        response_content = await self._generate_response(message, context)
        
        # Analyze for handoff opportunities
        handoff_decision = self._analyze_for_handoff(message, response_content, context)
        
        # Calculate confidence
        confidence = self._calculate_confidence(message, response_content, context)
        
        # Add AI response to history
        self.conversation_history.append(AIMessage(content=response_content))
        
        return AgentResponse(
            content=response_content,
            agent_role=self.role,
            handoff_decision=handoff_decision,
            confidence=confidence,
            metadata={
                "domain": self.domain.value,
                "tools_available": len(self.tools),
                "conversation_length": len(self.conversation_history)
            }
        )
    
    async def _generate_response(self, message: str, context: Dict[str, Any]) -> str:
        """Generate the actual response content."""
        
        # Create prompt template
        prompt = ChatPromptTemplate.from_messages([
            ("system", self.system_prompt),
            ("human", "{user_message}")
        ])
        
        # Create chain
        chain = prompt | self.llm | StrOutputParser()
        
        # Generate response
        response = await chain.ainvoke({
            "user_message": message,
            "context": context
        })
        
        return response
    
    def _analyze_for_handoff(
        self,
        user_message: str,
        response: str,
        context: Dict[str, Any]
    ) -> Optional[HandoffDecision]:
        """Analyze whether to hand off to another agent."""
        
        # Check for handoff keywords in user message
        for agent_role, keywords in self.handoff_keywords.items():
            for keyword in keywords:
                if keyword.lower() in user_message.lower():
                    return HandoffDecision(
                        should_handoff=True,
                        next_agent=agent_role,
                        confidence=0.8,
                        reason=f"User message contains keyword '{keyword}' suggesting {agent_role} expertise needed"
                    )
        
        # Check if response indicates uncertainty or need for specialist
        uncertainty_indicators = [
            "i'm not sure",
            "you might want to ask",
            "for more detailed",
            "specialized help",
            "beyond my expertise"
        ]
        
        for indicator in uncertainty_indicators:
            if indicator in response.lower():
                return HandoffDecision(
                    should_handoff=True,
                    next_agent=self._suggest_specialist(user_message, context),
                    confidence=0.6,
                    reason=f"Response indicates uncertainty: '{indicator}'"
                )
        
        return None
    
    def _suggest_specialist(self, message: str, context: Dict[str, Any]) -> str:
        """Suggest a specialist agent based on message content."""
        
        # Simple keyword-based suggestion
        if any(word in message.lower() for word in ["code", "programming", "implement", "write"]):
            return AgentRole.CODE_ASSISTANT.value
        elif any(word in message.lower() for word in ["practice", "exercise", "hands-on"]):
            return AgentRole.PRACTICE_FACILITATOR.value
        elif any(word in message.lower() for word in ["documentation", "docs", "reference"]):
            return AgentRole.DOCUMENTATION_EXPERT.value
        elif any(word in message.lower() for word in ["research", "find", "search"]):
            return AgentRole.RESEARCH_ASSISTANT.value
        elif any(word in message.lower() for word in ["project", "build", "create"]):
            return AgentRole.PROJECT_GUIDE.value
        elif any(word in message.lower() for word in ["error", "bug", "problem", "issue"]):
            return AgentRole.TROUBLESHOOTER.value
        elif any(word in message.lower() for word in ["help", "stuck", "confused"]):
            return AgentRole.MENTOR.value
        else:
            return AgentRole.INSTRUCTOR.value
    
    def _calculate_confidence(
        self,
        message: str,
        response: str,
        context: Dict[str, Any]
    ) -> float:
        """Calculate confidence in the response."""
        
        # Base confidence
        confidence = 0.8
        
        # Adjust based on response length (very short responses might indicate uncertainty)
        if len(response) < 50:
            confidence -= 0.2
        elif len(response) > 500:
            confidence += 0.1
        
        # Adjust based on uncertainty indicators
        uncertainty_words = ["maybe", "perhaps", "might", "could be", "not sure"]
        uncertainty_count = sum(1 for word in uncertainty_words if word in response.lower())
        confidence -= uncertainty_count * 0.1
        
        # Adjust based on domain expertise match
        domain_keywords = self._get_domain_keywords()
        keyword_matches = sum(1 for keyword in domain_keywords if keyword in message.lower())
        if keyword_matches > 0:
            confidence += min(keyword_matches * 0.05, 0.2)
        
        return max(0.0, min(1.0, confidence))
    
    def _get_domain_keywords(self) -> List[str]:
        """Get keywords relevant to this agent's domain."""
        domain_keywords = {
            AgentDomain.KNOWLEDGE: ["learn", "understand", "concept", "theory", "explain"],
            AgentDomain.PRACTICE: ["code", "implement", "practice", "exercise", "hands-on"],
            AgentDomain.SUPPORT: ["help", "guide", "mentor", "support", "stuck"],
            AgentDomain.ASSESSMENT: ["test", "quiz", "assess", "evaluate", "progress"]
        }
        return domain_keywords.get(self.domain, [])
    
    def get_agent_info(self) -> Dict[str, Any]:
        """Get information about this agent."""
        return {
            "role": self.role.value,
            "domain": self.domain.value,
            "tools_count": len(self.tools),
            "conversation_length": len(self.conversation_history)
        }
