"""Tests for core GAAPF functionality."""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime

from pyframeworks_assistant.core.learning_hub import LearningHubCore
from pyframeworks_assistant.core.constellation import ConstellationManager
from pyframeworks_assistant.core.constellation_types import (
    ConstellationType, 
    get_constellation_config,
    recommend_constellation_for_profile
)
from pyframeworks_assistant.core.temporal_state import TemporalStateManager
from pyframeworks_assistant.config.framework_configs import SupportedFrameworks


class TestConstellationTypes:
    """Test constellation type functionality."""
    
    def test_get_constellation_config(self):
        """Test getting constellation configuration."""
        config = get_constellation_config(ConstellationType.KNOWLEDGE_INTENSIVE)
        
        assert config.constellation_type == ConstellationType.KNOWLEDGE_INTENSIVE
        assert config.name == "Knowledge Intensive"
        assert config.theoretical_weight == 0.8
        assert config.practical_weight == 0.2
        assert "instructor" in config.primary_agents
    
    def test_recommend_constellation_for_profile(self):
        """Test constellation recommendation."""
        recommendations = recommend_constellation_for_profile(
            skill_level="intermediate",
            learning_style="hands_on",
            learning_pace="fast"
        )
        
        assert isinstance(recommendations, list)
        assert len(recommendations) > 0
        assert ConstellationType.HANDS_ON_FOCUSED in recommendations
    
    def test_all_constellations_have_configs(self):
        """Test that all constellation types have valid configurations."""
        for constellation_type in ConstellationType:
            config = get_constellation_config(constellation_type)
            assert config is not None
            assert config.constellation_type == constellation_type
            assert len(config.primary_agents) > 0


class TestConstellationManager:
    """Test constellation manager functionality."""
    
    @pytest.mark.asyncio
    async def test_create_constellation(self, mock_llm, sample_user_profile, temp_dir):
        """Test creating a constellation."""
        manager = ConstellationManager(mock_llm)
        
        # Mock the checkpointer to avoid SQLite dependency
        with patch('pyframeworks_assistant.core.constellation.SqliteSaver') as mock_saver:
            mock_saver.from_conn_string.return_value = Mock()
            
            constellation = await manager.create_constellation(
                constellation_type=ConstellationType.HANDS_ON_FOCUSED,
                user_profile=sample_user_profile,
                framework=SupportedFrameworks.LANGCHAIN,
                module_id="lc_basics",
                session_id="test_session"
            )
            
            assert constellation is not None
            assert "test_session" in manager.active_constellations
    
    def test_get_constellation_status(self, mock_llm):
        """Test getting constellation status."""
        manager = ConstellationManager(mock_llm)
        
        # Test non-existent session
        status = manager.get_constellation_status("non_existent")
        assert status is None
    
    def test_end_session(self, mock_llm):
        """Test ending a session."""
        manager = ConstellationManager(mock_llm)
        
        # Test non-existent session
        summary = manager.end_session("non_existent")
        assert summary is None


class TestTemporalStateManager:
    """Test temporal state management."""
    
    @pytest.mark.asyncio
    async def test_initialization(self, temp_dir):
        """Test temporal state manager initialization."""
        manager = TemporalStateManager(str(temp_dir))
        await manager._load_historical_data()
        
        assert manager.data_dir == temp_dir
        assert isinstance(manager.session_cache, dict)
        assert isinstance(manager.pattern_cache, dict)
    
    @pytest.mark.asyncio
    async def test_record_session(self, temp_dir, sample_user_profile, sample_session_data):
        """Test recording a learning session."""
        manager = TemporalStateManager(str(temp_dir))
        
        await manager.record_session(
            session_id="test_session",
            user_profile=sample_user_profile,
            constellation_type=ConstellationType.HANDS_ON_FOCUSED,
            framework=SupportedFrameworks.LANGCHAIN,
            module_id="lc_basics",
            session_data=sample_session_data
        )
        
        assert "test_session" in manager.session_cache
        session = manager.session_cache["test_session"]
        assert session.user_id == sample_user_profile.user_id
        assert session.constellation_type == ConstellationType.HANDS_ON_FOCUSED
    
    @pytest.mark.asyncio
    async def test_optimize_constellation_selection(self, temp_dir, sample_user_profile):
        """Test constellation optimization."""
        manager = TemporalStateManager(str(temp_dir))
        
        constellation_type, confidence = await manager.optimize_constellation_selection(
            user_profile=sample_user_profile,
            framework=SupportedFrameworks.LANGCHAIN,
            module_id="lc_basics",
            session_context={}
        )
        
        assert isinstance(constellation_type, ConstellationType)
        assert 0.0 <= confidence <= 1.0
    
    def test_get_user_learning_analytics(self, temp_dir):
        """Test getting user learning analytics."""
        manager = TemporalStateManager(str(temp_dir))
        
        # Test with non-existent user
        analytics = manager.get_user_learning_analytics("non_existent_user")
        assert "error" in analytics
        
        # Test with user that has sessions
        # This would require setting up session data first


class TestLearningHubCore:
    """Test learning hub core functionality."""
    
    @pytest.mark.asyncio
    async def test_initialization(self, learning_hub):
        """Test learning hub initialization."""
        assert learning_hub.is_initialized
        assert learning_hub.constellation_manager is not None
        assert learning_hub.temporal_manager is not None
        assert learning_hub.knowledge_graph is not None
        assert learning_hub.analytics_engine is not None
    
    @pytest.mark.asyncio
    async def test_start_learning_session(self, learning_hub, sample_user_profile):
        """Test starting a learning session."""
        session_id, session_info = await learning_hub.start_learning_session(
            user_profile=sample_user_profile,
            framework=SupportedFrameworks.LANGCHAIN,
            module_id="lc_basics"
        )
        
        assert session_id is not None
        assert isinstance(session_info, dict)
        assert "constellation_type" in session_info
        assert "framework" in session_info
        assert "module_title" in session_info
        assert session_id in learning_hub.active_sessions
    
    @pytest.mark.asyncio
    async def test_process_user_message(self, learning_hub, sample_user_profile):
        """Test processing user messages."""
        # Start a session first
        session_id, _ = await learning_hub.start_learning_session(
            user_profile=sample_user_profile,
            framework=SupportedFrameworks.LANGCHAIN,
            module_id="lc_basics"
        )
        
        # Mock the constellation manager's run_session method
        with patch.object(learning_hub.constellation_manager, 'run_session') as mock_run:
            mock_run.return_value = {
                "response": "Mock agent response",
                "agent_role": "instructor",
                "confidence": 0.8,
                "session_stats": {"total_interactions": 1}
            }
            
            result = await learning_hub.process_user_message(
                session_id=session_id,
                user_message="What is LangChain?"
            )
            
            assert "response" in result
            assert "agent_role" in result
            assert "session_id" in result
    
    @pytest.mark.asyncio
    async def test_end_learning_session(self, learning_hub, sample_user_profile):
        """Test ending a learning session."""
        # Start a session first
        session_id, _ = await learning_hub.start_learning_session(
            user_profile=sample_user_profile,
            framework=SupportedFrameworks.LANGCHAIN,
            module_id="lc_basics"
        )
        
        # Mock the constellation manager's end_session method
        with patch.object(learning_hub.constellation_manager, 'end_session') as mock_end:
            mock_end.return_value = {
                "session_id": session_id,
                "total_interactions": 5,
                "agents_used": 2,
                "handoff_chain": ["instructor", "code_assistant"]
            }
            
            summary = await learning_hub.end_learning_session(
                session_id=session_id,
                user_feedback={"satisfaction": 4}
            )
            
            assert "session_id" in summary
            assert "duration_minutes" in summary
            assert "constellation_type" in summary
            assert session_id not in learning_hub.active_sessions
    
    def test_get_session_status(self, learning_hub):
        """Test getting session status."""
        # Test non-existent session
        status = learning_hub.get_session_status("non_existent")
        assert status is None
    
    def test_get_active_sessions(self, learning_hub):
        """Test getting active sessions."""
        sessions = learning_hub.get_active_sessions()
        assert isinstance(sessions, list)
