"""Tests for GAAPF agents."""

import pytest
from unittest.mock import Mo<PERSON>, As<PERSON><PERSON><PERSON>, patch

from pyframeworks_assistant.agents.base_agent import Base<PERSON><PERSON>, <PERSON><PERSON><PERSON>, AgentDomain, HandoffDecision
from pyframeworks_assistant.agents.agent_factory import AgentFactory
from pyframeworks_assistant.agents.knowledge_agents import Instructor<PERSON>gent, DocumentationExpertAgent
from pyframeworks_assistant.agents.practice_agents import CodeAssistantAgent, PracticeFacilitatorAgent
from pyframeworks_assistant.agents.support_agents import MentorAgent, MotivationalCoachAgent
from pyframeworks_assistant.agents.assessment_agents import AssessmentAgent, ProgressTrackerAgent


class TestBaseAgent:
    """Test base agent functionality."""
    
    def test_agent_initialization(self, mock_llm):
        """Test agent initialization."""
        # Create a concrete implementation for testing
        class TestAgent(BaseAgent):
            def _create_system_prompt(self):
                return "Test system prompt"
            
            def _define_handoff_keywords(self):
                return {"test_agent": ["test", "help"]}
        
        agent = TestAgent(
            role=AgentRole.INSTRUCTOR,
            domain=AgentDomain.KNOWLEDGE,
            llm=mock_llm
        )
        
        assert agent.role == AgentRole.INSTRUCTOR
        assert agent.domain == AgentDomain.KNOWLEDGE
        assert agent.llm == mock_llm
        assert agent.system_prompt == "Test system prompt"
        assert "test_agent" in agent.handoff_keywords
    
    @pytest.mark.asyncio
    async def test_process_message(self, mock_llm):
        """Test processing a message."""
        class TestAgent(BaseAgent):
            def _create_system_prompt(self):
                return "Test system prompt"
            
            def _define_handoff_keywords(self):
                return {"code_assistant": ["code", "implement"]}
        
        agent = TestAgent(
            role=AgentRole.INSTRUCTOR,
            domain=AgentDomain.KNOWLEDGE,
            llm=mock_llm
        )
        
        # Mock the LLM chain
        with patch('pyframeworks_assistant.agents.base_agent.ChatPromptTemplate') as mock_prompt:
            with patch('pyframeworks_assistant.agents.base_agent.StrOutputParser') as mock_parser:
                mock_chain = AsyncMock()
                mock_chain.ainvoke = AsyncMock(return_value="Test response")
                
                # Mock the chain creation
                mock_prompt.from_messages.return_value.__or__ = Mock(return_value=mock_chain)
                
                response = await agent.process_message(
                    message="Tell me about LangChain",
                    context={"framework": "langchain"}
                )
                
                assert response.content == "Test response"
                assert response.agent_role == AgentRole.INSTRUCTOR
                assert response.confidence > 0
    
    def test_handoff_analysis(self, mock_llm):
        """Test handoff decision analysis."""
        class TestAgent(BaseAgent):
            def _create_system_prompt(self):
                return "Test system prompt"
            
            def _define_handoff_keywords(self):
                return {"code_assistant": ["code", "implement"]}
        
        agent = TestAgent(
            role=AgentRole.INSTRUCTOR,
            domain=AgentDomain.KNOWLEDGE,
            llm=mock_llm
        )
        
        # Test handoff trigger
        handoff = agent._analyze_for_handoff(
            user_message="Can you help me implement some code?",
            response="Sure, I can help with that",
            context={}
        )
        
        assert handoff is not None
        assert handoff.should_handoff
        assert handoff.next_agent == "code_assistant"
    
    def test_confidence_calculation(self, mock_llm):
        """Test confidence calculation."""
        class TestAgent(BaseAgent):
            def _create_system_prompt(self):
                return "Test system prompt"
            
            def _define_handoff_keywords(self):
                return {}
        
        agent = TestAgent(
            role=AgentRole.INSTRUCTOR,
            domain=AgentDomain.KNOWLEDGE,
            llm=mock_llm
        )
        
        # Test with good response
        confidence = agent._calculate_confidence(
            message="What is LangChain?",
            response="LangChain is a framework for building applications with language models. It provides tools and abstractions for working with LLMs.",
            context={}
        )
        
        assert 0.0 <= confidence <= 1.0
        
        # Test with uncertain response
        confidence_uncertain = agent._calculate_confidence(
            message="What is LangChain?",
            response="I'm not sure about that.",
            context={}
        )
        
        assert confidence_uncertain < confidence


class TestAgentFactory:
    """Test agent factory functionality."""
    
    def test_create_agent(self, agent_factory):
        """Test creating individual agents."""
        instructor = agent_factory.create_agent(AgentRole.INSTRUCTOR)
        assert isinstance(instructor, InstructorAgent)
        assert instructor.role == AgentRole.INSTRUCTOR
        
        code_assistant = agent_factory.create_agent(AgentRole.CODE_ASSISTANT)
        assert isinstance(code_assistant, CodeAssistantAgent)
        assert code_assistant.role == AgentRole.CODE_ASSISTANT
    
    def test_get_or_create_agent(self, agent_factory):
        """Test getting or creating agents with caching."""
        # First call should create
        agent1 = agent_factory.get_or_create_agent(AgentRole.INSTRUCTOR)
        
        # Second call should return cached instance
        agent2 = agent_factory.get_or_create_agent(AgentRole.INSTRUCTOR)
        
        assert agent1 is agent2
    
    def test_create_constellation_agents(self, agent_factory):
        """Test creating a constellation of agents."""
        roles = [AgentRole.INSTRUCTOR, AgentRole.CODE_ASSISTANT, AgentRole.MENTOR]
        agents = agent_factory.create_constellation_agents(roles)
        
        assert len(agents) == 3
        assert AgentRole.INSTRUCTOR in agents
        assert AgentRole.CODE_ASSISTANT in agents
        assert AgentRole.MENTOR in agents
        
        assert isinstance(agents[AgentRole.INSTRUCTOR], InstructorAgent)
        assert isinstance(agents[AgentRole.CODE_ASSISTANT], CodeAssistantAgent)
        assert isinstance(agents[AgentRole.MENTOR], MentorAgent)
    
    def test_get_available_roles(self, agent_factory):
        """Test getting available agent roles."""
        roles = agent_factory.get_available_roles()
        
        assert AgentRole.INSTRUCTOR in roles
        assert AgentRole.CODE_ASSISTANT in roles
        assert AgentRole.MENTOR in roles
        assert len(roles) == len(AgentRole)
    
    def test_get_agent_info(self, agent_factory):
        """Test getting agent information."""
        info = agent_factory.get_agent_info(AgentRole.INSTRUCTOR)
        
        assert info["role"] == "instructor"
        assert info["domain"] == "knowledge"
        assert "class_name" in info
        assert "description" in info
    
    def test_invalid_agent_role(self, agent_factory):
        """Test handling invalid agent roles."""
        with pytest.raises(ValueError):
            agent_factory.create_agent("invalid_role")


class TestKnowledgeAgents:
    """Test knowledge domain agents."""
    
    def test_instructor_agent(self, mock_llm):
        """Test instructor agent."""
        agent = InstructorAgent(mock_llm)
        
        assert agent.role == AgentRole.INSTRUCTOR
        assert agent.domain == AgentDomain.KNOWLEDGE
        assert "teaching" in agent.system_prompt.lower()
        assert "code_assistant" in agent.handoff_keywords
    
    def test_documentation_expert_agent(self, mock_llm):
        """Test documentation expert agent."""
        agent = DocumentationExpertAgent(mock_llm)
        
        assert agent.role == AgentRole.DOCUMENTATION_EXPERT
        assert agent.domain == AgentDomain.KNOWLEDGE
        assert "documentation" in agent.system_prompt.lower()
        assert "instructor" in agent.handoff_keywords


class TestPracticeAgents:
    """Test practice domain agents."""
    
    def test_code_assistant_agent(self, mock_llm):
        """Test code assistant agent."""
        agent = CodeAssistantAgent(mock_llm)
        
        assert agent.role == AgentRole.CODE_ASSISTANT
        assert agent.domain == AgentDomain.PRACTICE
        assert "code" in agent.system_prompt.lower()
        assert "instructor" in agent.handoff_keywords
    
    def test_practice_facilitator_agent(self, mock_llm):
        """Test practice facilitator agent."""
        agent = PracticeFacilitatorAgent(mock_llm)
        
        assert agent.role == AgentRole.PRACTICE_FACILITATOR
        assert agent.domain == AgentDomain.PRACTICE
        assert "practice" in agent.system_prompt.lower()
        assert "code_assistant" in agent.handoff_keywords


class TestSupportAgents:
    """Test support domain agents."""
    
    def test_mentor_agent(self, mock_llm):
        """Test mentor agent."""
        agent = MentorAgent(mock_llm)
        
        assert agent.role == AgentRole.MENTOR
        assert agent.domain == AgentDomain.SUPPORT
        assert "mentor" in agent.system_prompt.lower()
        assert "instructor" in agent.handoff_keywords
    
    def test_motivational_coach_agent(self, mock_llm):
        """Test motivational coach agent."""
        agent = MotivationalCoachAgent(mock_llm)
        
        assert agent.role == AgentRole.MOTIVATIONAL_COACH
        assert agent.domain == AgentDomain.SUPPORT
        assert "motivation" in agent.system_prompt.lower()
        assert "mentor" in agent.handoff_keywords


class TestAssessmentAgents:
    """Test assessment domain agents."""
    
    def test_assessment_agent(self, mock_llm):
        """Test assessment agent."""
        agent = AssessmentAgent(mock_llm)
        
        assert agent.role == AgentRole.ASSESSMENT
        assert agent.domain == AgentDomain.ASSESSMENT
        assert "assessment" in agent.system_prompt.lower()
        assert "instructor" in agent.handoff_keywords
    
    def test_progress_tracker_agent(self, mock_llm):
        """Test progress tracker agent."""
        agent = ProgressTrackerAgent(mock_llm)
        
        assert agent.role == AgentRole.PROGRESS_TRACKER
        assert agent.domain == AgentDomain.ASSESSMENT
        assert "progress" in agent.system_prompt.lower()
        assert "mentor" in agent.handoff_keywords
