"""User memory for profile and progress tracking."""

import json
import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from pathlib import Path
from pydantic import BaseModel, Field


class SessionRecord(BaseModel):
    """Record of a learning session."""
    
    session_id: str
    timestamp: datetime
    duration_minutes: float
    interactions_count: int
    framework: Optional[str] = None
    module_id: Optional[str] = None
    constellation_type: Optional[str] = None
    completion_rate: float = 0.0
    satisfaction_score: float = 0.0


class LearningStreak(BaseModel):
    """Tracks learning streaks and consistency."""
    
    current_streak: int = 0
    longest_streak: int = 0
    last_learning_date: Optional[datetime] = None
    total_learning_days: int = 0
    
    def update_streak(self, learning_date: datetime) -> None:
        """Update streak information."""
        learning_date = learning_date.date()
        
        if self.last_learning_date:
            last_date = self.last_learning_date.date()
            days_diff = (learning_date - last_date).days
            
            if days_diff == 1:
                # Consecutive day
                self.current_streak += 1
            elif days_diff > 1:
                # Streak broken
                self.current_streak = 1
            # Same day doesn't change streak
        else:
            # First learning session
            self.current_streak = 1
            self.total_learning_days = 1
        
        # Update longest streak
        self.longest_streak = max(self.longest_streak, self.current_streak)
        
        # Update last learning date if it's a new day
        if not self.last_learning_date or learning_date > self.last_learning_date.date():
            self.last_learning_date = datetime.combine(learning_date, datetime.min.time())
            if self.last_learning_date.date() != learning_date:
                self.total_learning_days += 1


class LearningGoal(BaseModel):
    """Represents a learning goal."""
    
    goal_id: str
    title: str
    description: str
    target_date: Optional[datetime] = None
    progress: float = Field(0.0, ge=0.0, le=1.0)
    status: str = "active"  # active, completed, paused, cancelled
    created_at: datetime = Field(default_factory=datetime.now)
    completed_at: Optional[datetime] = None
    
    def update_progress(self, progress: float) -> None:
        """Update goal progress."""
        self.progress = max(0.0, min(1.0, progress))
        
        if self.progress >= 1.0 and self.status == "active":
            self.status = "completed"
            self.completed_at = datetime.now()


class UserMemory:
    """Manages user-specific memory and progress tracking."""
    
    def __init__(self, data_dir: Path):
        """Initialize user memory.
        
        Args:
            data_dir: Directory for storing user data
        """
        self.data_dir = data_dir
        self.data_dir.mkdir(exist_ok=True)
        
        # User data storage
        self.user_sessions: Dict[str, List[SessionRecord]] = {}
        self.user_streaks: Dict[str, LearningStreak] = {}
        self.user_goals: Dict[str, List[LearningGoal]] = {}
        self.user_preferences: Dict[str, Dict[str, Any]] = {}
        
        self.is_initialized = False
    
    async def initialize(self) -> None:
        """Initialize user memory system."""
        if self.is_initialized:
            return
        
        # Load existing user data
        await self._load_user_data()
        
        self.is_initialized = True
    
    async def _load_user_data(self) -> None:
        """Load user data from storage."""
        try:
            # Load session records
            sessions_file = self.data_dir / "user_sessions.json"
            if sessions_file.exists():
                with open(sessions_file, 'r') as f:
                    sessions_data = json.load(f)
                
                for user_id, sessions in sessions_data.items():
                    self.user_sessions[user_id] = [
                        SessionRecord(**session) for session in sessions
                    ]
            
            # Load streaks
            streaks_file = self.data_dir / "user_streaks.json"
            if streaks_file.exists():
                with open(streaks_file, 'r') as f:
                    streaks_data = json.load(f)
                
                for user_id, streak_data in streaks_data.items():
                    self.user_streaks[user_id] = LearningStreak(**streak_data)
            
            # Load goals
            goals_file = self.data_dir / "user_goals.json"
            if goals_file.exists():
                with open(goals_file, 'r') as f:
                    goals_data = json.load(f)
                
                for user_id, goals in goals_data.items():
                    self.user_goals[user_id] = [
                        LearningGoal(**goal) for goal in goals
                    ]
            
            # Load preferences
            prefs_file = self.data_dir / "user_preferences.json"
            if prefs_file.exists():
                with open(prefs_file, 'r') as f:
                    self.user_preferences = json.load(f)
        
        except Exception as e:
            print(f"Warning: Could not load user data: {e}")
    
    async def add_session_record(
        self,
        user_id: str,
        session_summary: Dict[str, Any]
    ) -> None:
        """Add a session record for a user.
        
        Args:
            user_id: User identifier
            session_summary: Summary of the completed session
        """
        session_record = SessionRecord(
            session_id=session_summary.get("session_id", ""),
            timestamp=session_summary.get("end_time", datetime.now()),
            duration_minutes=session_summary.get("duration_minutes", 0),
            interactions_count=session_summary.get("interactions_count", 0),
            framework=session_summary.get("framework"),
            module_id=session_summary.get("module_id"),
            constellation_type=session_summary.get("constellation_type"),
            completion_rate=session_summary.get("completion_rate", 0.0),
            satisfaction_score=session_summary.get("satisfaction_score", 0.0)
        )
        
        if user_id not in self.user_sessions:
            self.user_sessions[user_id] = []
        
        self.user_sessions[user_id].append(session_record)
        
        # Update learning streak
        await self._update_learning_streak(user_id, session_record.timestamp)
        
        # Persist changes
        await self._persist_user_data()
    
    async def _update_learning_streak(self, user_id: str, session_date: datetime) -> None:
        """Update user's learning streak."""
        if user_id not in self.user_streaks:
            self.user_streaks[user_id] = LearningStreak()
        
        self.user_streaks[user_id].update_streak(session_date)
    
    async def update_learning_progress(
        self,
        user_id: str,
        concepts_learned: List[str],
        skills_practiced: List[str],
        session_summary: Dict[str, Any]
    ) -> None:
        """Update user's learning progress.
        
        Args:
            user_id: User identifier
            concepts_learned: Concepts learned in session
            skills_practiced: Skills practiced in session
            session_summary: Session summary with progress information
        """
        # Update goal progress based on concepts learned
        if user_id in self.user_goals:
            for goal in self.user_goals[user_id]:
                if goal.status == "active":
                    # Simple progress update based on concepts learned
                    progress_increment = len(concepts_learned) * 0.1  # 10% per concept
                    new_progress = goal.progress + progress_increment
                    goal.update_progress(new_progress)
        
        # Update user preferences based on session performance
        await self._update_user_preferences(user_id, session_summary)
        
        # Persist changes
        await self._persist_user_data()
    
    async def _update_user_preferences(
        self,
        user_id: str,
        session_summary: Dict[str, Any]
    ) -> None:
        """Update user preferences based on session performance."""
        if user_id not in self.user_preferences:
            self.user_preferences[user_id] = {
                "preferred_session_length": 30,
                "preferred_constellation_types": {},
                "learning_patterns": {}
            }
        
        prefs = self.user_preferences[user_id]
        
        # Update preferred session length
        duration = session_summary.get("duration_minutes", 30)
        satisfaction = session_summary.get("satisfaction_score", 0.5)
        
        if satisfaction > 0.7:
            # Good session - adjust preference toward this duration
            current_pref = prefs.get("preferred_session_length", 30)
            prefs["preferred_session_length"] = (current_pref * 0.8 + duration * 0.2)
        
        # Update constellation preferences
        constellation_type = session_summary.get("constellation_type")
        if constellation_type:
            if constellation_type not in prefs["preferred_constellation_types"]:
                prefs["preferred_constellation_types"][constellation_type] = []
            
            prefs["preferred_constellation_types"][constellation_type].append(satisfaction)
            
            # Keep only recent scores
            if len(prefs["preferred_constellation_types"][constellation_type]) > 10:
                prefs["preferred_constellation_types"][constellation_type] = \
                    prefs["preferred_constellation_types"][constellation_type][-10:]
    
    async def create_learning_goal(
        self,
        user_id: str,
        title: str,
        description: str,
        target_date: Optional[datetime] = None
    ) -> str:
        """Create a new learning goal for a user.
        
        Args:
            user_id: User identifier
            title: Goal title
            description: Goal description
            target_date: Optional target completion date
            
        Returns:
            Goal ID
        """
        goal_id = f"{user_id}_goal_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        goal = LearningGoal(
            goal_id=goal_id,
            title=title,
            description=description,
            target_date=target_date
        )
        
        if user_id not in self.user_goals:
            self.user_goals[user_id] = []
        
        self.user_goals[user_id].append(goal)
        
        # Persist changes
        await self._persist_user_data()
        
        return goal_id
    
    async def get_user_context(self, user_id: str) -> Dict[str, Any]:
        """Get user context for personalization.
        
        Args:
            user_id: User identifier
            
        Returns:
            User context information
        """
        sessions = self.user_sessions.get(user_id, [])
        streak = self.user_streaks.get(user_id, LearningStreak())
        goals = self.user_goals.get(user_id, [])
        preferences = self.user_preferences.get(user_id, {})
        
        # Calculate recent performance
        recent_sessions = sorted(sessions, key=lambda x: x.timestamp)[-5:]
        avg_satisfaction = (
            sum(s.satisfaction_score for s in recent_sessions) / len(recent_sessions)
            if recent_sessions else 0.5
        )
        
        # Active goals
        active_goals = [g for g in goals if g.status == "active"]
        
        return {
            "user_id": user_id,
            "total_sessions": len(sessions),
            "current_streak": streak.current_streak,
            "longest_streak": streak.longest_streak,
            "total_learning_days": streak.total_learning_days,
            "recent_satisfaction": avg_satisfaction,
            "active_goals": len(active_goals),
            "preferences": preferences,
            "learning_consistency": self._calculate_consistency(sessions),
            "preferred_learning_times": self._analyze_learning_times(sessions)
        }
    
    def _calculate_consistency(self, sessions: List[SessionRecord]) -> str:
        """Calculate learning consistency."""
        if len(sessions) < 3:
            return "insufficient_data"
        
        # Calculate days between sessions
        sorted_sessions = sorted(sessions, key=lambda x: x.timestamp)
        gaps = []
        
        for i in range(1, len(sorted_sessions)):
            gap = (sorted_sessions[i].timestamp - sorted_sessions[i-1].timestamp).days
            gaps.append(gap)
        
        avg_gap = sum(gaps) / len(gaps)
        
        if avg_gap <= 2:
            return "very_consistent"
        elif avg_gap <= 5:
            return "consistent"
        elif avg_gap <= 10:
            return "moderate"
        else:
            return "inconsistent"
    
    def _analyze_learning_times(self, sessions: List[SessionRecord]) -> Dict[str, int]:
        """Analyze preferred learning times."""
        time_counts = {"morning": 0, "afternoon": 0, "evening": 0, "night": 0}
        
        for session in sessions:
            hour = session.timestamp.hour
            
            if 6 <= hour < 12:
                time_counts["morning"] += 1
            elif 12 <= hour < 17:
                time_counts["afternoon"] += 1
            elif 17 <= hour < 22:
                time_counts["evening"] += 1
            else:
                time_counts["night"] += 1
        
        return time_counts
    
    async def get_user_summary(self, user_id: str) -> Dict[str, Any]:
        """Get user memory summary."""
        context = await self.get_user_context(user_id)
        sessions = self.user_sessions.get(user_id, [])
        goals = self.user_goals.get(user_id, [])
        
        # Calculate total learning time
        total_time = sum(s.duration_minutes for s in sessions)
        
        # Goal completion rate
        completed_goals = len([g for g in goals if g.status == "completed"])
        total_goals = len(goals)
        goal_completion_rate = completed_goals / total_goals if total_goals > 0 else 0.0
        
        return {
            "system": "user_memory",
            "total_sessions": len(sessions),
            "total_learning_time_minutes": total_time,
            "current_streak": context["current_streak"],
            "learning_consistency": context["learning_consistency"],
            "goal_completion_rate": goal_completion_rate,
            "active_goals": len([g for g in goals if g.status == "active"]),
            "average_session_satisfaction": context["recent_satisfaction"]
        }
    
    async def _persist_user_data(self) -> None:
        """Persist user data to storage."""
        try:
            # Save sessions
            sessions_data = {}
            for user_id, sessions in self.user_sessions.items():
                sessions_data[user_id] = [session.dict() for session in sessions]
            
            with open(self.data_dir / "user_sessions.json", 'w') as f:
                json.dump(sessions_data, f, indent=2, default=str)
            
            # Save streaks
            streaks_data = {}
            for user_id, streak in self.user_streaks.items():
                streaks_data[user_id] = streak.dict()
            
            with open(self.data_dir / "user_streaks.json", 'w') as f:
                json.dump(streaks_data, f, indent=2, default=str)
            
            # Save goals
            goals_data = {}
            for user_id, goals in self.user_goals.items():
                goals_data[user_id] = [goal.dict() for goal in goals]
            
            with open(self.data_dir / "user_goals.json", 'w') as f:
                json.dump(goals_data, f, indent=2, default=str)
            
            # Save preferences
            with open(self.data_dir / "user_preferences.json", 'w') as f:
                json.dump(self.user_preferences, f, indent=2, default=str)
        
        except Exception as e:
            print(f"Warning: Could not persist user data: {e}")
    
    async def cleanup_old_data(self, cutoff_date: datetime) -> None:
        """Clean up old user data.
        
        Args:
            cutoff_date: Date before which to remove data
        """
        # Remove old sessions
        for user_id in self.user_sessions:
            self.user_sessions[user_id] = [
                session for session in self.user_sessions[user_id]
                if session.timestamp >= cutoff_date
            ]
        
        # Remove completed goals older than cutoff
        for user_id in self.user_goals:
            self.user_goals[user_id] = [
                goal for goal in self.user_goals[user_id]
                if goal.status != "completed" or 
                (goal.completed_at and goal.completed_at >= cutoff_date)
            ]
