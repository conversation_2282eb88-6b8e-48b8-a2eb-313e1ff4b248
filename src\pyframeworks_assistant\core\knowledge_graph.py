"""Knowledge graph manager for concept relationships and learning paths."""

import json
import asyncio
from typing import Dict, List, Optional, Any, Set, Tuple
from datetime import datetime
from pathlib import Path
from pydantic import BaseModel, Field
from collections import defaultdict, deque

from ..config.framework_configs import SupportedFrameworks


class ConceptNode(BaseModel):
    """Represents a concept in the knowledge graph."""
    
    concept_id: str
    name: str
    description: str
    framework: SupportedFrameworks
    module_id: Optional[str] = None
    
    # Learning metadata
    difficulty_level: int = Field(1, ge=1, le=5)
    prerequisites: List[str] = Field(default_factory=list)
    learning_objectives: List[str] = Field(default_factory=list)
    
    # Usage tracking
    times_discussed: int = 0
    last_discussed: Optional[datetime] = None
    user_mastery_scores: Dict[str, float] = Field(default_factory=dict)


class ConceptRelationship(BaseModel):
    """Represents a relationship between concepts."""
    
    from_concept: str
    to_concept: str
    relationship_type: str  # "prerequisite", "related", "builds_on", "example_of"
    strength: float = Field(1.0, ge=0.0, le=1.0)
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.now)
    evidence_count: int = 1  # Number of times this relationship was observed


class LearningPath(BaseModel):
    """Represents an optimal learning path through concepts."""
    
    path_id: str
    name: str
    framework: SupportedFrameworks
    concept_sequence: List[str]
    estimated_duration_minutes: int
    difficulty_level: int = Field(1, ge=1, le=5)
    
    # Path metadata
    success_rate: float = 0.0
    user_completions: int = 0
    created_at: datetime = Field(default_factory=datetime.now)


class KnowledgeGraphManager:
    """Manager for knowledge graph construction and navigation."""
    
    def __init__(self, data_dir: str = "knowledge_graph"):
        """Initialize the knowledge graph manager.
        
        Args:
            data_dir: Directory for storing knowledge graph data
        """
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True)
        
        # Graph storage
        self.concepts: Dict[str, ConceptNode] = {}
        self.relationships: List[ConceptRelationship] = []
        self.learning_paths: Dict[str, LearningPath] = {}
        
        # Adjacency lists for efficient graph operations
        self.prerequisite_graph: Dict[str, List[str]] = defaultdict(list)
        self.related_concepts: Dict[str, List[str]] = defaultdict(list)
        
        # User progress tracking
        self.user_concept_progress: Dict[str, Dict[str, float]] = defaultdict(dict)
        
        self.is_initialized = False
    
    async def initialize(self) -> None:
        """Initialize the knowledge graph with framework concepts."""
        if self.is_initialized:
            return
        
        # Load existing graph data
        await self._load_graph_data()
        
        # Initialize with framework concepts if empty
        if not self.concepts:
            await self._initialize_framework_concepts()
        
        # Build adjacency lists
        self._build_adjacency_lists()
        
        self.is_initialized = True
    
    async def _load_graph_data(self) -> None:
        """Load knowledge graph data from storage."""
        try:
            # Load concepts
            concepts_file = self.data_dir / "concepts.json"
            if concepts_file.exists():
                with open(concepts_file, 'r') as f:
                    concepts_data = json.load(f)
                    for concept_data in concepts_data:
                        concept = ConceptNode(**concept_data)
                        self.concepts[concept.concept_id] = concept
            
            # Load relationships
            relationships_file = self.data_dir / "relationships.json"
            if relationships_file.exists():
                with open(relationships_file, 'r') as f:
                    relationships_data = json.load(f)
                    self.relationships = [
                        ConceptRelationship(**rel_data) for rel_data in relationships_data
                    ]
            
            # Load learning paths
            paths_file = self.data_dir / "learning_paths.json"
            if paths_file.exists():
                with open(paths_file, 'r') as f:
                    paths_data = json.load(f)
                    for path_data in paths_data:
                        path = LearningPath(**path_data)
                        self.learning_paths[path.path_id] = path
            
        except Exception as e:
            print(f"Warning: Could not load knowledge graph data: {e}")
    
    async def _initialize_framework_concepts(self) -> None:
        """Initialize knowledge graph with basic framework concepts."""
        
        # LangChain concepts
        langchain_concepts = [
            {
                "concept_id": "lc_llm",
                "name": "Language Models (LLM)",
                "description": "Large Language Models and their integration in LangChain",
                "framework": SupportedFrameworks.LANGCHAIN,
                "module_id": "lc_basics",
                "difficulty_level": 1,
                "learning_objectives": ["Understand LLM basics", "Learn LLM integration"]
            },
            {
                "concept_id": "lc_prompts",
                "name": "Prompt Templates",
                "description": "Creating and managing prompt templates",
                "framework": SupportedFrameworks.LANGCHAIN,
                "module_id": "lc_basics",
                "difficulty_level": 1,
                "prerequisites": ["lc_llm"],
                "learning_objectives": ["Create prompt templates", "Use variables in prompts"]
            },
            {
                "concept_id": "lc_chains",
                "name": "Chains",
                "description": "Connecting components in processing pipelines",
                "framework": SupportedFrameworks.LANGCHAIN,
                "module_id": "lc_chains",
                "difficulty_level": 2,
                "prerequisites": ["lc_llm", "lc_prompts"],
                "learning_objectives": ["Build basic chains", "Understand chain composition"]
            },
            {
                "concept_id": "lc_memory",
                "name": "Memory Systems",
                "description": "Managing conversation memory and context",
                "framework": SupportedFrameworks.LANGCHAIN,
                "module_id": "lc_memory",
                "difficulty_level": 2,
                "prerequisites": ["lc_chains"],
                "learning_objectives": ["Implement memory", "Manage context"]
            },
            {
                "concept_id": "lc_agents",
                "name": "Agents",
                "description": "Intelligent agents with tool integration",
                "framework": SupportedFrameworks.LANGCHAIN,
                "module_id": "lc_agents",
                "difficulty_level": 3,
                "prerequisites": ["lc_chains", "lc_memory"],
                "learning_objectives": ["Create agents", "Integrate tools"]
            }
        ]
        
        # Add concepts to graph
        for concept_data in langchain_concepts:
            concept = ConceptNode(**concept_data)
            self.concepts[concept.concept_id] = concept
        
        # Create relationships
        relationships = [
            ("lc_llm", "lc_prompts", "prerequisite"),
            ("lc_prompts", "lc_chains", "prerequisite"),
            ("lc_chains", "lc_memory", "prerequisite"),
            ("lc_chains", "lc_agents", "prerequisite"),
            ("lc_memory", "lc_agents", "prerequisite"),
            ("lc_prompts", "lc_memory", "related"),
        ]
        
        for from_concept, to_concept, rel_type in relationships:
            relationship = ConceptRelationship(
                from_concept=from_concept,
                to_concept=to_concept,
                relationship_type=rel_type
            )
            self.relationships.append(relationship)
        
        # Save initial data
        await self._persist_graph_data()
    
    def _build_adjacency_lists(self) -> None:
        """Build adjacency lists for efficient graph operations."""
        self.prerequisite_graph.clear()
        self.related_concepts.clear()
        
        for relationship in self.relationships:
            if relationship.relationship_type == "prerequisite":
                self.prerequisite_graph[relationship.to_concept].append(relationship.from_concept)
            elif relationship.relationship_type == "related":
                self.related_concepts[relationship.from_concept].append(relationship.to_concept)
                self.related_concepts[relationship.to_concept].append(relationship.from_concept)
    
    async def update_from_interaction(
        self,
        user_id: str,
        framework: SupportedFrameworks,
        module_id: str,
        concepts_discussed: List[str]
    ) -> None:
        """Update knowledge graph based on learning interaction.
        
        Args:
            user_id: User identifier
            framework: Framework being learned
            module_id: Module within framework
            concepts_discussed: List of concepts mentioned in interaction
        """
        current_time = datetime.now()
        
        # Update concept usage statistics
        for concept_name in concepts_discussed:
            concept_id = self._find_concept_id(concept_name, framework)
            if concept_id and concept_id in self.concepts:
                concept = self.concepts[concept_id]
                concept.times_discussed += 1
                concept.last_discussed = current_time
                
                # Update user mastery (simplified scoring)
                if user_id not in concept.user_mastery_scores:
                    concept.user_mastery_scores[user_id] = 0.1
                else:
                    # Increment mastery with diminishing returns
                    current_mastery = concept.user_mastery_scores[user_id]
                    increment = 0.1 * (1.0 - current_mastery)
                    concept.user_mastery_scores[user_id] = min(1.0, current_mastery + increment)
        
        # Discover new relationships between discussed concepts
        await self._discover_concept_relationships(concepts_discussed, framework)
        
        # Update user progress
        self.user_concept_progress[user_id].update({
            concept: self.concepts[self._find_concept_id(concept, framework)].user_mastery_scores.get(user_id, 0.0)
            for concept in concepts_discussed
            if self._find_concept_id(concept, framework)
        })
        
        # Persist changes
        await self._persist_graph_data()
    
    def _find_concept_id(self, concept_name: str, framework: SupportedFrameworks) -> Optional[str]:
        """Find concept ID by name and framework."""
        concept_name_lower = concept_name.lower()
        
        for concept_id, concept in self.concepts.items():
            if (concept.framework == framework and 
                (concept_name_lower in concept.name.lower() or 
                 concept_name_lower in concept.description.lower())):
                return concept_id
        
        return None
    
    async def _discover_concept_relationships(
        self,
        concepts_discussed: List[str],
        framework: SupportedFrameworks
    ) -> None:
        """Discover new relationships between concepts discussed together."""
        
        if len(concepts_discussed) < 2:
            return
        
        # Find concept IDs
        concept_ids = []
        for concept_name in concepts_discussed:
            concept_id = self._find_concept_id(concept_name, framework)
            if concept_id:
                concept_ids.append(concept_id)
        
        # Create or strengthen relationships between co-occurring concepts
        for i, concept_a in enumerate(concept_ids):
            for concept_b in concept_ids[i+1:]:
                # Check if relationship already exists
                existing_rel = None
                for rel in self.relationships:
                    if ((rel.from_concept == concept_a and rel.to_concept == concept_b) or
                        (rel.from_concept == concept_b and rel.to_concept == concept_a)):
                        existing_rel = rel
                        break
                
                if existing_rel:
                    # Strengthen existing relationship
                    existing_rel.evidence_count += 1
                    existing_rel.strength = min(1.0, existing_rel.strength + 0.1)
                else:
                    # Create new "related" relationship
                    new_rel = ConceptRelationship(
                        from_concept=concept_a,
                        to_concept=concept_b,
                        relationship_type="related",
                        strength=0.3
                    )
                    self.relationships.append(new_rel)
    
    def get_learning_path(
        self,
        target_concept: str,
        user_id: str,
        framework: SupportedFrameworks
    ) -> Optional[List[str]]:
        """Get optimal learning path to a target concept.
        
        Args:
            target_concept: Target concept to learn
            user_id: User identifier for personalization
            framework: Framework context
            
        Returns:
            Ordered list of concept IDs to learn
        """
        target_id = self._find_concept_id(target_concept, framework)
        if not target_id:
            return None
        
        # Get user's current mastery levels
        user_mastery = self.user_concept_progress.get(user_id, {})
        
        # Use topological sort to find prerequisite order
        path = self._topological_sort_to_concept(target_id, user_mastery)
        
        return path
    
    def _topological_sort_to_concept(
        self,
        target_concept_id: str,
        user_mastery: Dict[str, float]
    ) -> List[str]:
        """Find learning path using topological sort."""
        
        # Build subgraph of concepts needed for target
        needed_concepts = set()
        queue = deque([target_concept_id])
        
        while queue:
            concept_id = queue.popleft()
            if concept_id in needed_concepts:
                continue
            
            needed_concepts.add(concept_id)
            
            # Add prerequisites
            for prereq in self.prerequisite_graph.get(concept_id, []):
                if prereq not in needed_concepts:
                    queue.append(prereq)
        
        # Topological sort of needed concepts
        in_degree = defaultdict(int)
        for concept_id in needed_concepts:
            for prereq in self.prerequisite_graph.get(concept_id, []):
                if prereq in needed_concepts:
                    in_degree[concept_id] += 1
        
        # Start with concepts that have no prerequisites
        queue = deque([concept_id for concept_id in needed_concepts if in_degree[concept_id] == 0])
        path = []
        
        while queue:
            concept_id = queue.popleft()
            
            # Only include if user hasn't mastered it yet
            if user_mastery.get(concept_id, 0.0) < 0.8:  # 80% mastery threshold
                path.append(concept_id)
            
            # Update in-degrees for dependent concepts
            for rel in self.relationships:
                if (rel.from_concept == concept_id and 
                    rel.to_concept in needed_concepts and
                    rel.relationship_type == "prerequisite"):
                    in_degree[rel.to_concept] -= 1
                    if in_degree[rel.to_concept] == 0:
                        queue.append(rel.to_concept)
        
        return path
    
    def get_related_concepts(self, concept_name: str, framework: SupportedFrameworks) -> List[str]:
        """Get concepts related to the given concept."""
        concept_id = self._find_concept_id(concept_name, framework)
        if not concept_id:
            return []
        
        related = []
        for related_id in self.related_concepts.get(concept_id, []):
            if related_id in self.concepts:
                related.append(self.concepts[related_id].name)
        
        return related
    
    def get_user_knowledge_map(self, user_id: str) -> Dict[str, Any]:
        """Get user's knowledge map with mastery levels."""
        user_progress = self.user_concept_progress.get(user_id, {})
        
        knowledge_map = {
            "total_concepts": len(self.concepts),
            "concepts_learned": len([c for c in user_progress.values() if c > 0.5]),
            "concepts_mastered": len([c for c in user_progress.values() if c > 0.8]),
            "mastery_by_framework": {},
            "concept_details": {}
        }
        
        # Group by framework
        for concept_id, concept in self.concepts.items():
            framework_name = concept.framework.value
            if framework_name not in knowledge_map["mastery_by_framework"]:
                knowledge_map["mastery_by_framework"][framework_name] = {
                    "total": 0,
                    "learned": 0,
                    "mastered": 0
                }
            
            knowledge_map["mastery_by_framework"][framework_name]["total"] += 1
            mastery = user_progress.get(concept_id, 0.0)
            
            if mastery > 0.5:
                knowledge_map["mastery_by_framework"][framework_name]["learned"] += 1
            if mastery > 0.8:
                knowledge_map["mastery_by_framework"][framework_name]["mastered"] += 1
            
            knowledge_map["concept_details"][concept.name] = {
                "mastery": mastery,
                "difficulty": concept.difficulty_level,
                "times_discussed": concept.times_discussed
            }
        
        return knowledge_map
    
    async def _persist_graph_data(self) -> None:
        """Persist knowledge graph data to storage."""
        try:
            # Save concepts
            concepts_data = [concept.dict() for concept in self.concepts.values()]
            with open(self.data_dir / "concepts.json", 'w') as f:
                json.dump(concepts_data, f, indent=2, default=str)
            
            # Save relationships
            relationships_data = [rel.dict() for rel in self.relationships]
            with open(self.data_dir / "relationships.json", 'w') as f:
                json.dump(relationships_data, f, indent=2, default=str)
            
            # Save learning paths
            paths_data = [path.dict() for path in self.learning_paths.values()]
            with open(self.data_dir / "learning_paths.json", 'w') as f:
                json.dump(paths_data, f, indent=2, default=str)
                
        except Exception as e:
            print(f"Warning: Could not persist knowledge graph data: {e}")
