"""Main CLI interface for GAAPF system."""

import asyncio
import sys
from typing import Optional
import click
from rich.console import Console
from rich.panel import Panel
from rich.text import Text
from rich.prompt import Prompt, Confirm
from rich.table import Table
from rich.markdown import Markdown

from ...config.system_config import system_config
from ...config.user_profiles import UserProfile, SkillLevel, LearningPace, LearningStyle
from ...config.framework_configs import SupportedFrameworks, get_framework_config
from ...core.learning_hub import LearningHubCore
from .llm_setup import setup_llm
from .profile_manager import ProfileManager
from .session_manager import SessionManager


console = Console()


class CLILearningSystem:
    """Main CLI learning system for GAAPF."""
    
    def __init__(self):
        self.learning_hub: Optional[LearningHubCore] = None
        self.profile_manager = ProfileManager()
        self.session_manager = SessionManager()
        self.current_user: Optional[UserProfile] = None
        self.current_session_id: Optional[str] = None
    
    async def initialize(self) -> bool:
        """Initialize the CLI learning system."""
        try:
            # Setup LLM
            llm = setup_llm()
            if not llm:
                console.print("[red]❌ Failed to setup LLM. Please check your API keys.[/red]")
                return False
            
            # Initialize learning hub
            self.learning_hub = LearningHubCore(llm)
            await self.learning_hub.initialize()
            
            console.print("[green]✅ GAAPF system initialized successfully![/green]")
            return True
        
        except Exception as e:
            console.print(f"[red]❌ Failed to initialize system: {e}[/red]")
            return False
    
    async def run(self) -> None:
        """Run the main CLI loop."""
        # Display welcome message
        self._display_welcome()
        
        # Initialize system
        if not await self.initialize():
            return
        
        # User onboarding
        self.current_user = await self._user_onboarding()
        if not self.current_user:
            return
        
        # Main learning loop
        await self._main_learning_loop()
    
    def _display_welcome(self) -> None:
        """Display welcome message."""
        welcome_text = Text()
        welcome_text.append("🤖 Welcome to ", style="bold blue")
        welcome_text.append("GAAPF", style="bold magenta")
        welcome_text.append(" - Guidance AI Agent for Python Framework", style="bold blue")
        
        description = """
An Adaptive Multi-Agent Learning System for AI Framework Education

✨ Features:
• 12 Specialized AI Agents for personalized learning
• Adaptive Learning Constellations that adjust to your style
• Real-time LLM integration with Google Gemini, OpenAI, or Claude
• Temporal optimization that learns from your progress
• Hands-on coding exercises and project guidance
        """
        
        console.print(Panel(
            welcome_text,
            subtitle="🚀 Let's start your AI framework learning journey!",
            border_style="blue"
        ))
        console.print(description)
    
    async def _user_onboarding(self) -> Optional[UserProfile]:
        """Handle user onboarding process."""
        console.print("\n[bold cyan]👤 User Profile Setup[/bold cyan]")
        
        # Check for existing profile
        existing_users = self.profile_manager.list_users()
        if existing_users:
            console.print("\n[yellow]Found existing user profiles:[/yellow]")
            for i, user_id in enumerate(existing_users, 1):
                console.print(f"  {i}. {user_id}")
            
            choice = Prompt.ask(
                "\nWould you like to",
                choices=["load", "new"],
                default="load"
            )
            
            if choice == "load":
                user_choice = Prompt.ask(
                    "Enter user number or ID",
                    default="1"
                )
                
                try:
                    if user_choice.isdigit():
                        user_id = existing_users[int(user_choice) - 1]
                    else:
                        user_id = user_choice
                    
                    profile = self.profile_manager.load_profile(user_id)
                    if profile:
                        console.print(f"[green]✅ Loaded profile for {profile.name or profile.user_id}[/green]")
                        return profile
                except (IndexError, ValueError):
                    console.print("[red]Invalid selection. Creating new profile.[/red]")
        
        # Create new profile
        return await self._create_new_profile()
    
    async def _create_new_profile(self) -> Optional[UserProfile]:
        """Create a new user profile."""
        console.print("\n[bold cyan]📝 Creating New User Profile[/bold cyan]")
        
        try:
            # Basic information
            name = Prompt.ask("What's your name?", default="Anonymous")
            user_id = Prompt.ask("Choose a user ID", default=name.lower().replace(" ", "_"))
            
            # Experience assessment
            console.print("\n[bold]Experience Assessment:[/bold]")
            
            programming_years = int(Prompt.ask(
                "Years of programming experience",
                default="2"
            ))
            
            python_skill = Prompt.ask(
                "Python skill level",
                choices=["none", "beginner", "intermediate", "advanced", "expert"],
                default="intermediate"
            )
            
            ai_experience = Prompt.ask(
                "AI/ML experience level",
                choices=["none", "beginner", "intermediate", "advanced", "expert"],
                default="beginner"
            )
            
            # Learning preferences
            console.print("\n[bold]Learning Preferences:[/bold]")
            
            learning_style = Prompt.ask(
                "Preferred learning style",
                choices=["visual", "hands_on", "theoretical", "mixed"],
                default="mixed"
            )
            
            learning_pace = Prompt.ask(
                "Preferred learning pace",
                choices=["slow", "moderate", "fast", "intensive"],
                default="moderate"
            )
            
            # Goals
            console.print("\n[bold]Learning Goals:[/bold]")
            goals_input = Prompt.ask(
                "What do you want to learn? (comma-separated)",
                default="LangChain basics, Build RAG applications"
            )
            goals = [goal.strip() for goal in goals_input.split(",")]
            
            # Create profile
            profile = UserProfile(
                user_id=user_id,
                name=name,
                programming_experience_years=programming_years,
                python_skill_level=SkillLevel(python_skill),
                ai_ml_experience=SkillLevel(ai_experience),
                preferred_learning_style=LearningStyle(learning_style),
                learning_pace=LearningPace(learning_pace),
                learning_goals=goals
            )
            
            # Save profile
            self.profile_manager.save_profile(profile)
            
            console.print(f"[green]✅ Profile created for {name}![/green]")
            return profile
        
        except Exception as e:
            console.print(f"[red]❌ Error creating profile: {e}[/red]")
            return None
    
    async def _main_learning_loop(self) -> None:
        """Main learning interaction loop."""
        console.print(f"\n[bold green]🎓 Welcome to your learning session, {self.current_user.name}![/bold green]")
        
        # Framework selection
        framework = await self._select_framework()
        if not framework:
            return
        
        # Module selection
        module_id = await self._select_module(framework)
        if not module_id:
            return
        
        # Start learning session
        session_id, session_info = await self.learning_hub.start_learning_session(
            user_profile=self.current_user,
            framework=framework,
            module_id=module_id
        )
        
        self.current_session_id = session_id
        
        # Display session info
        self._display_session_info(session_info)
        
        # Learning interaction loop
        await self._learning_interaction_loop()
    
    async def _select_framework(self) -> Optional[SupportedFrameworks]:
        """Let user select a framework to learn."""
        console.print("\n[bold cyan]🔧 Framework Selection[/bold cyan]")
        
        frameworks = [
            ("langchain", "LangChain - Framework for LLM applications"),
            ("langgraph", "LangGraph - Stateful multi-agent workflows")
        ]
        
        console.print("Available frameworks:")
        for i, (framework_id, description) in enumerate(frameworks, 1):
            console.print(f"  {i}. {description}")
        
        choice = Prompt.ask(
            "Select framework",
            choices=["1", "2", "langchain", "langgraph"],
            default="1"
        )
        
        if choice in ["1", "langchain"]:
            return SupportedFrameworks.LANGCHAIN
        elif choice in ["2", "langgraph"]:
            return SupportedFrameworks.LANGGRAPH
        
        return None
    
    async def _select_module(self, framework: SupportedFrameworks) -> Optional[str]:
        """Let user select a module within the framework."""
        console.print(f"\n[bold cyan]📚 Module Selection for {framework.value.title()}[/bold cyan]")
        
        framework_config = get_framework_config(framework)
        modules = list(framework_config.modules.items())
        
        if not modules:
            console.print("[red]No modules available for this framework.[/red]")
            return None
        
        console.print("Available modules:")
        for i, (module_id, module_config) in enumerate(modules, 1):
            console.print(f"  {i}. {module_config.title}")
            console.print(f"     {module_config.description}")
            console.print(f"     Difficulty: {module_config.difficulty_level}/5, Duration: {module_config.estimated_duration_minutes} min")
            console.print()
        
        choice = Prompt.ask(
            "Select module",
            choices=[str(i) for i in range(1, len(modules) + 1)] + [m[0] for m in modules],
            default="1"
        )
        
        if choice.isdigit():
            return modules[int(choice) - 1][0]
        else:
            return choice if choice in [m[0] for m in modules] else None
    
    def _display_session_info(self, session_info: dict) -> None:
        """Display session information."""
        table = Table(title="Learning Session Info")
        table.add_column("Property", style="cyan")
        table.add_column("Value", style="green")
        
        table.add_row("Session ID", session_info["session_id"])
        table.add_row("Constellation Type", session_info["constellation_type"].replace("_", " ").title())
        table.add_row("Framework", session_info["framework"].title())
        table.add_row("Module", session_info["module_title"])
        table.add_row("Estimated Duration", f"{session_info['estimated_duration']} minutes")
        table.add_row("Experience Level", session_info["user_experience_level"].title())
        
        console.print(table)
        console.print()
    
    async def _learning_interaction_loop(self) -> None:
        """Main learning interaction loop."""
        console.print("[bold cyan]💬 Learning Chat - Type your questions or 'quit' to end session[/bold cyan]")
        console.print("[dim]Available commands: /help, /status, /progress, /quit[/dim]\n")
        
        while True:
            try:
                # Get user input
                user_input = Prompt.ask("[bold blue]You[/bold blue]").strip()
                
                if not user_input:
                    continue
                
                # Handle commands
                if user_input.startswith("/"):
                    if await self._handle_command(user_input):
                        break
                    continue
                
                if user_input.lower() in ["quit", "exit", "bye"]:
                    break
                
                # Process with learning hub
                console.print("[dim]🤖 Processing...[/dim]")
                
                result = await self.learning_hub.process_user_message(
                    session_id=self.current_session_id,
                    user_message=user_input
                )
                
                if "error" in result:
                    console.print(f"[red]❌ {result['error']}[/red]")
                    continue
                
                # Display agent response
                agent_role = result.get("agent_role", "assistant")
                response = result.get("response", "No response")
                confidence = result.get("confidence", 0.0)
                
                # Format agent name
                agent_name = agent_role.replace("_", " ").title()
                agent_emoji = self._get_agent_emoji(agent_role)
                
                console.print(f"\n[bold green]{agent_emoji} {agent_name}[/bold green]")
                console.print(Panel(
                    Markdown(response),
                    border_style="green",
                    title=f"Confidence: {confidence:.1%}"
                ))
                
                # Show handoff suggestion if available
                if result.get("handoff_suggestion"):
                    next_agent = result["handoff_suggestion"].replace("_", " ").title()
                    console.print(f"[dim]💡 Suggestion: Continue with {next_agent} for specialized help[/dim]")
                
                console.print()
            
            except KeyboardInterrupt:
                console.print("\n[yellow]Session interrupted by user[/yellow]")
                break
            except Exception as e:
                console.print(f"[red]❌ Error: {e}[/red]")
        
        # End session
        await self._end_session()
    
    def _get_agent_emoji(self, agent_role: str) -> str:
        """Get emoji for agent role."""
        emoji_map = {
            "instructor": "👨‍🏫",
            "code_assistant": "💻",
            "documentation_expert": "📚",
            "research_assistant": "🔍",
            "practice_facilitator": "🏋️",
            "project_guide": "🏗️",
            "troubleshooter": "🔧",
            "mentor": "🎯",
            "motivational_coach": "💪",
            "assessment": "📊",
            "progress_tracker": "📈",
            "knowledge_synthesizer": "🧠"
        }
        return emoji_map.get(agent_role, "🤖")
    
    async def _handle_command(self, command: str) -> bool:
        """Handle CLI commands. Returns True if should exit."""
        command = command.lower().strip()
        
        if command == "/help":
            self._show_help()
        elif command == "/status":
            await self._show_status()
        elif command == "/progress":
            await self._show_progress()
        elif command in ["/quit", "/exit"]:
            return True
        else:
            console.print(f"[red]Unknown command: {command}[/red]")
        
        return False
    
    def _show_help(self) -> None:
        """Show help information."""
        help_text = """
[bold cyan]Available Commands:[/bold cyan]

/help     - Show this help message
/status   - Show current session status
/progress - Show learning progress
/quit     - End the learning session

[bold cyan]Learning Tips:[/bold cyan]

• Ask specific questions about the framework you're learning
• Request code examples and explanations
• Ask for practice exercises or projects
• Get help with debugging and troubleshooting
• Request assessments to test your knowledge

[bold cyan]Example Questions:[/bold cyan]

• "What is a LangChain chain and how do I create one?"
• "Show me a code example of using prompt templates"
• "I'm getting an error with my agent setup, can you help?"
• "Give me a practice exercise for memory management"
        """
        console.print(Panel(help_text, title="Help", border_style="cyan"))
    
    async def _show_status(self) -> None:
        """Show current session status."""
        if not self.current_session_id:
            console.print("[red]No active session[/red]")
            return
        
        status = self.learning_hub.get_session_status(self.current_session_id)
        if not status:
            console.print("[red]Could not get session status[/red]")
            return
        
        table = Table(title="Session Status")
        table.add_column("Property", style="cyan")
        table.add_column("Value", style="green")
        
        table.add_row("Session ID", status["session_id"])
        table.add_row("Duration", f"{status['duration_minutes']:.1f} minutes")
        table.add_row("Interactions", str(status["interactions_count"]))
        table.add_row("Framework", status["framework"].title())
        table.add_row("Module", status["module_id"])
        
        if status.get("constellation_status"):
            const_status = status["constellation_status"]
            table.add_row("Current Agent", const_status.get("current_agent", "None"))
            table.add_row("Total Interactions", str(const_status.get("total_interactions", 0)))
        
        console.print(table)
    
    async def _show_progress(self) -> None:
        """Show learning progress."""
        # This would integrate with the analytics engine
        console.print("[yellow]Progress tracking coming soon![/yellow]")
    
    async def _end_session(self) -> None:
        """End the current learning session."""
        if not self.current_session_id:
            return
        
        console.print("\n[bold cyan]📝 Ending Learning Session[/bold cyan]")
        
        # Get user feedback
        satisfaction = Prompt.ask(
            "How satisfied were you with this session? (1-5)",
            choices=["1", "2", "3", "4", "5"],
            default="4"
        )
        
        feedback = {
            "satisfaction_score": int(satisfaction) / 5.0,
            "user_comments": Prompt.ask("Any additional feedback?", default="")
        }
        
        # End session
        summary = await self.learning_hub.end_learning_session(
            session_id=self.current_session_id,
            user_feedback=feedback
        )
        
        # Display summary
        if summary and "error" not in summary:
            console.print("\n[bold green]📊 Session Summary[/bold green]")
            
            table = Table()
            table.add_column("Metric", style="cyan")
            table.add_column("Value", style="green")
            
            table.add_row("Duration", f"{summary['duration_minutes']:.1f} minutes")
            table.add_row("Interactions", str(summary["interactions_count"]))
            table.add_row("Constellation", summary["constellation_type"].replace("_", " ").title())
            table.add_row("Completion", f"{summary.get('completion_estimate', 0):.1%}")
            
            console.print(table)
            
            if summary.get("next_recommendations"):
                console.print("\n[bold cyan]🎯 Next Steps:[/bold cyan]")
                for rec in summary["next_recommendations"]:
                    console.print(f"• {rec}")
        
        console.print("\n[bold green]✅ Thank you for learning with GAAPF![/bold green]")
        console.print("[dim]Your progress has been saved. See you next time![/dim]")


@click.command()
@click.option("--debug", is_flag=True, help="Enable debug mode")
def main(debug: bool) -> None:
    """GAAPF - Guidance AI Agent for Python Framework CLI."""
    if debug:
        import logging
        logging.basicConfig(level=logging.DEBUG)
    
    # Check system configuration
    if not system_config.has_llm_api_key():
        console.print("[red]❌ No LLM API keys found![/red]")
        console.print("Please set at least one of the following environment variables:")
        console.print("• GOOGLE_API_KEY (for Google Gemini)")
        console.print("• OPENAI_API_KEY (for OpenAI GPT)")
        console.print("• ANTHROPIC_API_KEY (for Anthropic Claude)")
        console.print("\nSee env.example for configuration details.")
        sys.exit(1)
    
    # Run the CLI system
    try:
        cli_system = CLILearningSystem()
        asyncio.run(cli_system.run())
    except KeyboardInterrupt:
        console.print("\n[yellow]Goodbye![/yellow]")
    except Exception as e:
        console.print(f"[red]❌ Unexpected error: {e}[/red]")
        if debug:
            raise


if __name__ == "__main__":
    main()
