"""
Advanced usage example for GAAPF system.

This example demonstrates:
1. Custom constellation configuration
2. Analytics and progress tracking
3. Memory system usage
4. Tool integration
5. Multi-session learning patterns
"""

import asyncio
import json
from pathlib import Path
import sys

# Add the src directory to the path for local development
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from pyframeworks_assistant import (
    LearningHubCore,
    UserProfile,
    SupportedFrameworks,
    SkillLevel,
    LearningStyle,
    LearningPace,
)
from pyframeworks_assistant.interfaces.cli.llm_setup import setup_llm
from pyframeworks_assistant.core.constellation_types import get_constellation_config, get_available_constellations


async def demonstrate_constellation_analysis():
    """Demonstrate constellation analysis and selection."""
    print("\n🌟 Constellation Analysis Demo")
    print("-" * 40)
    
    # Show all available constellations
    print("Available Learning Constellations:")
    for constellation_type in get_available_constellations():
        config = get_constellation_config(constellation_type)
        print(f"\n📋 {config.name}")
        print(f"   Type: {constellation_type.value}")
        print(f"   Theory/Practice: {config.theoretical_weight:.0%}/{config.practical_weight:.0%}")
        print(f"   Primary agents: {', '.join(config.primary_agents)}")
        print(f"   Support agents: {', '.join(config.support_agents)}")
        print(f"   Description: {config.description}")


async def demonstrate_multi_session_learning(hub: LearningHubCore, profile: UserProfile):
    """Demonstrate learning across multiple sessions."""
    print("\n📚 Multi-Session Learning Demo")
    print("-" * 40)
    
    sessions = []
    
    # Session 1: LangChain Basics
    print("\n🎓 Session 1: LangChain Basics")
    session1_id, session1_info = await hub.start_learning_session(
        user_profile=profile,
        framework=SupportedFrameworks.LANGCHAIN,
        module_id="lc_basics"
    )
    sessions.append(session1_id)
    
    print(f"   Constellation: {session1_info['constellation_type']}")
    
    # Simulate some interactions
    await hub.process_user_message(
        session_id=session1_id,
        user_message="What are the core components of LangChain?"
    )
    
    await hub.process_user_message(
        session_id=session1_id,
        user_message="How do I create a simple chain?"
    )
    
    # End session 1
    summary1 = await hub.end_learning_session(
        session1_id,
        user_feedback={"satisfaction_score": 0.8}
    )
    print(f"   Completed in {summary1['duration_minutes']:.1f} minutes")
    
    # Session 2: Chains and Composition
    print("\n🎓 Session 2: Chains and Composition")
    session2_id, session2_info = await hub.start_learning_session(
        user_profile=profile,
        framework=SupportedFrameworks.LANGCHAIN,
        module_id="lc_chains"
    )
    sessions.append(session2_id)
    
    print(f"   Constellation: {session2_info['constellation_type']}")
    print(f"   Constellation confidence: {session2_info.get('constellation_confidence', 0):.1%}")
    
    # Simulate interactions
    await hub.process_user_message(
        session_id=session2_id,
        user_message="Show me how to chain multiple LLM calls together"
    )
    
    await hub.process_user_message(
        session_id=session2_id,
        user_message="I want to practice building a sequential chain"
    )
    
    # End session 2
    summary2 = await hub.end_learning_session(
        session2_id,
        user_feedback={"satisfaction_score": 0.9}
    )
    print(f"   Completed in {summary2['duration_minutes']:.1f} minutes")
    
    return sessions


async def demonstrate_analytics_insights(hub: LearningHubCore, profile: UserProfile):
    """Demonstrate analytics and insights generation."""
    print("\n📊 Analytics and Insights Demo")
    print("-" * 40)
    
    # Get user learning analytics
    analytics = hub.temporal_manager.get_user_learning_analytics(profile.user_id)
    
    if "error" not in analytics:
        print("📈 Learning Analytics:")
        print(f"   Total sessions: {analytics['total_sessions']}")
        print(f"   Total learning time: {analytics['total_learning_time_minutes']:.1f} minutes")
        print(f"   Average comprehension: {analytics['average_comprehension']:.1%}")
        print(f"   Average engagement: {analytics['average_engagement']:.1%}")
        print(f"   Recent performance trend: {analytics['recent_trend']}")
    else:
        print("📈 No analytics data available yet (need more sessions)")
    
    # Get knowledge map
    knowledge_map = hub.knowledge_graph.get_user_knowledge_map(profile.user_id)
    
    print("\n🧠 Knowledge Map:")
    print(f"   Total concepts: {knowledge_map['total_concepts']}")
    print(f"   Concepts learned: {knowledge_map['concepts_learned']}")
    print(f"   Concepts mastered: {knowledge_map['concepts_mastered']}")
    
    if knowledge_map['mastery_by_framework']:
        print("   Framework mastery:")
        for framework, mastery in knowledge_map['mastery_by_framework'].items():
            print(f"     {framework}: {mastery['learned']}/{mastery['total']} learned, {mastery['mastered']} mastered")


async def demonstrate_memory_system(hub: LearningHubCore, profile: UserProfile):
    """Demonstrate memory system capabilities."""
    print("\n🧠 Memory System Demo")
    print("-" * 40)
    
    # Get memory summary
    memory_summary = await hub.memory_manager.get_user_memory_summary(profile.user_id)
    
    print("💾 Memory Summary:")
    print(f"   User ID: {memory_summary['user_id']}")
    
    if memory_summary.get('conversation_memory'):
        conv_memory = memory_summary['conversation_memory']
        print(f"   Conversation sessions: {conv_memory.get('total_sessions', 0)}")
        print(f"   Total conversation turns: {conv_memory.get('total_conversation_turns', 0)}")
    
    if memory_summary.get('knowledge_memory'):
        know_memory = memory_summary['knowledge_memory']
        print(f"   Concepts exposed: {know_memory.get('concepts_exposed', 0)}")
        print(f"   Concepts learning: {know_memory.get('concepts_learning', 0)}")
        print(f"   Average mastery: {know_memory.get('average_mastery', 0):.1%}")
    
    if memory_summary.get('user_memory'):
        user_memory = memory_summary['user_memory']
        print(f"   Total sessions: {user_memory.get('total_sessions', 0)}")
        print(f"   Learning consistency: {user_memory.get('learning_consistency', 'unknown')}")


async def demonstrate_tool_integration(hub: LearningHubCore):
    """Demonstrate tool integration capabilities."""
    print("\n🔧 Tool Integration Demo")
    print("-" * 40)
    
    # Get tool information
    tool_info = hub.tool_manager.get_tool_info()
    
    print("🛠️ Available Tools:")
    print(f"   Total tools: {tool_info['total_tools']}")
    print(f"   Available: {len(tool_info['available_tools'])}")
    print(f"   Unavailable: {len(tool_info['unavailable_tools'])}")
    
    print("\n📂 Tool Categories:")
    for category, tools in tool_info['categories'].items():
        if tools:
            print(f"   {category.title()}: {', '.join(tools)}")
    
    if tool_info['available_tools']:
        print("\n🔍 Tool Details:")
        for tool in tool_info['available_tools'][:3]:  # Show first 3 tools
            print(f"   • {tool['name']}: {tool['description']}")


async def save_session_report(sessions: list, profile: UserProfile, hub: LearningHubCore):
    """Save a detailed session report."""
    print("\n📄 Generating Session Report")
    print("-" * 40)
    
    report = {
        "user_profile": {
            "user_id": profile.user_id,
            "name": profile.name,
            "experience_level": profile.get_experience_level(),
            "learning_style": profile.preferred_learning_style.value,
            "learning_pace": profile.learning_pace.value,
            "goals": profile.learning_goals
        },
        "sessions": [],
        "analytics": {},
        "generated_at": asyncio.get_event_loop().time()
    }
    
    # Add session summaries
    for session_id in sessions:
        # In a real implementation, you'd get session details from the hub
        report["sessions"].append({
            "session_id": session_id,
            "status": "completed"
        })
    
    # Add analytics
    analytics = hub.temporal_manager.get_user_learning_analytics(profile.user_id)
    if "error" not in analytics:
        report["analytics"] = analytics
    
    # Save report
    report_file = Path("examples") / f"session_report_{profile.user_id}.json"
    with open(report_file, 'w') as f:
        json.dump(report, f, indent=2, default=str)
    
    print(f"✅ Session report saved to: {report_file}")


async def main():
    """Main advanced example function."""
    print("🚀 GAAPF Advanced Usage Example")
    print("=" * 50)
    
    # Setup
    llm = setup_llm()
    if not llm:
        print("❌ No LLM provider available. Please set API keys in .env file.")
        return
    
    hub = LearningHubCore(llm)
    await hub.initialize()
    
    # Create an advanced user profile
    profile = UserProfile(
        user_id="advanced_user",
        name="Advanced User",
        programming_experience_years=5,
        python_skill_level=SkillLevel.ADVANCED,
        ai_ml_experience=SkillLevel.INTERMEDIATE,
        preferred_learning_style=LearningStyle.MIXED,
        learning_pace=LearningPace.FAST,
        learning_goals=[
            "Master LangChain architecture",
            "Build production RAG systems",
            "Understand advanced agent patterns",
            "Learn LangGraph workflows"
        ]
    )
    
    print(f"👤 Advanced User Profile: {profile.name}")
    print(f"   Experience: {profile.get_experience_level()}")
    print(f"   Goals: {len(profile.learning_goals)} learning objectives")
    
    # Run demonstrations
    await demonstrate_constellation_analysis()
    
    sessions = await demonstrate_multi_session_learning(hub, profile)
    
    await demonstrate_analytics_insights(hub, profile)
    
    await demonstrate_memory_system(hub, profile)
    
    await demonstrate_tool_integration(hub)
    
    await save_session_report(sessions, profile, hub)
    
    print("\n🎉 Advanced example completed!")
    print("\nKey takeaways:")
    print("• GAAPF adapts constellation selection based on user patterns")
    print("• Memory systems track learning progress across sessions")
    print("• Analytics provide insights for optimization")
    print("• Tool integration enables rich learning experiences")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n👋 Advanced example interrupted. Goodbye!")
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()
