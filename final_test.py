#!/usr/bin/env python3
"""Final verification test."""

import sys
from pathlib import Path

# Add src to path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

print("🚀 Final verification of GAAPF fixes...")

# Test 1: BaseSettings fix
print("\n1. Testing BaseSettings fix...")
try:
    from pyframeworks_assistant.config.system_config import SystemConfig, system_config
    config = SystemConfig()
    print(f"✅ SystemConfig works: log_level={config.log_level}")
except Exception as e:
    print(f"❌ SystemConfig failed: {e}")

# Test 2: Tool Manager
print("\n2. Testing ToolManager...")
try:
    from pyframeworks_assistant.tools.tool_manager import ToolManager
    tm = ToolManager()
    info = tm.get_tool_info()
    print(f"✅ ToolManager works: {len(info['available_tools'])}/{info['total_tools']} tools available")
    if info['unavailable_tools']:
        print(f"   Unavailable: {info['unavailable_tools']}")
except Exception as e:
    print(f"❌ ToolManager failed: {e}")

# Test 3: Tavily integration
print("\n3. Testing Tavily integration...")
try:
    from pyframeworks_assistant.tools.tavily_tools import create_tavily_search_tool, get_tavily_tools
    tool = create_tavily_search_tool("test_key")
    all_tools = get_tavily_tools("test_key")
    print(f"✅ Tavily integration works: {tool.name}, {len(all_tools)} tools total")
except Exception as e:
    print(f"❌ Tavily integration failed: {e}")

print("\n🎉 Verification complete!")
print("\n✅ Summary of successful fixes:")
print("   • Fixed BaseSettings import (pydantic -> pydantic-settings)")
print("   • Updated to Pydantic 2.x model_config syntax")
print("   • Fixed all tool classes for Pydantic 2.x compatibility")
print("   • Added modern Tavily integration with LangChain 0.3.x")
print("   • All tools now properly initialize with field definitions")
