#!/usr/bin/env python3
"""
GAAPF Setup Script

This script helps set up the GAAPF system with interactive configuration.
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path
from typing import Optional


def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 10):
        print("❌ Python 3.10 or higher is required.")
        print(f"   Current version: {sys.version}")
        return False
    
    print(f"✅ Python version: {sys.version.split()[0]}")
    return True


def check_git():
    """Check if git is available."""
    try:
        subprocess.run(["git", "--version"], capture_output=True, check=True)
        print("✅ Git is available")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("⚠️  Git not found (optional for local setup)")
        return False


def create_virtual_environment():
    """Create and activate virtual environment."""
    venv_path = Path("gaapf-env")
    
    if venv_path.exists():
        print("✅ Virtual environment already exists")
        return True
    
    try:
        print("🔧 Creating virtual environment...")
        subprocess.run([sys.executable, "-m", "venv", "gaapf-env"], check=True)
        print("✅ Virtual environment created")
        
        # Provide activation instructions
        if os.name == "nt":  # Windows
            activate_cmd = "gaapf-env\\Scripts\\activate"
        else:  # Unix/Linux/macOS
            activate_cmd = "source gaapf-env/bin/activate"
        
        print(f"💡 To activate: {activate_cmd}")
        return True
    
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to create virtual environment: {e}")
        return False


def install_dependencies():
    """Install project dependencies."""
    try:
        print("📦 Installing dependencies...")
        
        # Check if we're in a virtual environment
        in_venv = hasattr(sys, 'real_prefix') or (
            hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix
        )
        
        if not in_venv:
            print("⚠️  Not in virtual environment. Consider activating it first.")
        
        # Install requirements
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], check=True)
        
        print("✅ Dependencies installed")
        return True
    
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False


def setup_environment_file():
    """Set up environment configuration file."""
    env_file = Path(".env")
    env_example = Path("env.example")
    
    if env_file.exists():
        print("✅ .env file already exists")
        return True
    
    if not env_example.exists():
        print("⚠️  env.example not found, creating basic .env file")
        create_basic_env_file(env_file)
        return True
    
    try:
        shutil.copy(env_example, env_file)
        print("✅ Created .env file from template")
        print("💡 Please edit .env to add your API keys")
        return True
    
    except Exception as e:
        print(f"❌ Failed to create .env file: {e}")
        return False


def create_basic_env_file(env_file: Path):
    """Create a basic .env file with common settings."""
    content = """# GAAPF Configuration
# Add at least one LLM API key

# Google Gemini (recommended)
GOOGLE_API_KEY=your_google_gemini_api_key_here

# OpenAI GPT
OPENAI_API_KEY=your_openai_api_key_here

# Anthropic Claude
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Optional: Tavily for enhanced search
TAVILY_API_KEY=your_tavily_api_key_here

# Optional: LangSmith for tracing
LANGCHAIN_TRACING_V2=false
LANGCHAIN_API_KEY=your_langsmith_api_key_here
LANGCHAIN_PROJECT=gaapf-guidance-ai-agent

# System Configuration
GAAPF_LOG_LEVEL=INFO
GAAPF_MAX_CONCURRENT_AGENTS=16
GAAPF_CONSTELLATION_TIMEOUT=300
GAAPF_DEBUG=false
"""
    
    with open(env_file, 'w') as f:
        f.write(content)


def test_installation():
    """Test the installation."""
    try:
        print("🧪 Testing installation...")
        
        # Test basic import
        result = subprocess.run([
            sys.executable, "-c", 
            "from pyframeworks_assistant import get_version; print(f'GAAPF v{get_version()}')"
        ], capture_output=True, text=True, check=True)
        
        print(f"✅ {result.stdout.strip()}")
        
        # Test LLM provider availability
        result = subprocess.run([
            sys.executable, "-c",
            "from pyframeworks_assistant.interfaces.cli.llm_setup import get_available_providers; print('Providers:', get_available_providers())"
        ], capture_output=True, text=True, check=True)
        
        print(f"✅ {result.stdout.strip()}")
        return True
    
    except subprocess.CalledProcessError as e:
        print(f"❌ Installation test failed: {e}")
        if e.stderr:
            print(f"   Error: {e.stderr}")
        return False


def get_api_key_instructions():
    """Provide API key setup instructions."""
    instructions = """
🔑 API Key Setup Instructions:

1. Google Gemini (Recommended - Free tier available):
   • Go to: https://makersuite.google.com/app/apikey
   • Create API key and add to .env as GOOGLE_API_KEY

2. OpenAI GPT:
   • Go to: https://platform.openai.com/api-keys
   • Create API key and add to .env as OPENAI_API_KEY

3. Anthropic Claude:
   • Go to: https://console.anthropic.com/
   • Create API key and add to .env as ANTHROPIC_API_KEY

4. Tavily Search (Optional):
   • Go to: https://tavily.com/
   • Sign up and add API key to .env as TAVILY_API_KEY

After adding API keys, test with:
   python examples/basic_usage.py
"""
    return instructions


def main():
    """Main setup function."""
    print("🤖 GAAPF Setup Script")
    print("=" * 50)
    
    # Check prerequisites
    if not check_python_version():
        return False
    
    check_git()
    
    # Setup steps
    steps = [
        ("Create virtual environment", create_virtual_environment),
        ("Install dependencies", install_dependencies),
        ("Setup environment file", setup_environment_file),
        ("Test installation", test_installation),
    ]
    
    for step_name, step_func in steps:
        print(f"\n🔧 {step_name}...")
        if not step_func():
            print(f"❌ Setup failed at: {step_name}")
            return False
    
    # Success message
    print("\n🎉 GAAPF setup completed successfully!")
    print("\n📋 Next steps:")
    print("1. Edit .env file to add your API keys")
    print("2. Run: python examples/basic_usage.py")
    print("3. Or start CLI: gaapf-cli")
    print("4. Or web demo: streamlit run src/pyframeworks_assistant/interfaces/streamlit_demo.py")
    
    print(get_api_key_instructions())
    
    return True


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n👋 Setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
