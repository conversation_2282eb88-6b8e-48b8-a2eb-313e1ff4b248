"""File and code execution tools for GAAPF."""

import os
import subprocess
import tempfile
from typing import Dict, List, Optional, Any
from pathlib import Path
from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field


class FileWriteInput(BaseModel):
    """Input for file write tool."""
    filename: str = Field(..., description="Name of the file to write")
    content: str = Field(..., description="Content to write to the file")
    directory: str = Field("generated_code", description="Directory to write file in")


class FileReadInput(BaseModel):
    """Input for file read tool."""
    filename: str = Field(..., description="Name of the file to read")
    directory: str = Field("generated_code", description="Directory to read file from")


class CodeExecuteInput(BaseModel):
    """Input for code execution tool."""
    code: str = Field(..., description="Python code to execute")
    filename: Optional[str] = Field(None, description="Optional filename to save code")
    timeout: int = Field(30, description="Execution timeout in seconds")


class FileWriteTool(BaseTool):
    """Tool for writing files to the generated code directory."""
    
    name: str = "file_write"
    description: str = "Write content to a file in the generated code directory"
    args_schema = FileWriteInput
    
    def __init__(self):
        super().__init__()
        self.base_dir = Path("generated_code")
        self.base_dir.mkdir(exist_ok=True)
    
    def _run(self, filename: str, content: str, directory: str = "generated_code") -> str:
        """Write content to a file."""
        try:
            # Ensure directory exists
            target_dir = Path(directory)
            target_dir.mkdir(parents=True, exist_ok=True)
            
            # Validate filename (security check)
            if ".." in filename or filename.startswith("/"):
                return "Error: Invalid filename for security reasons"
            
            file_path = target_dir / filename
            
            # Write content to file
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            return f"Successfully wrote {len(content)} characters to {file_path}"
        
        except Exception as e:
            return f"Error writing file: {str(e)}"
    
    async def _arun(self, filename: str, content: str, directory: str = "generated_code") -> str:
        """Async version of file write."""
        return self._run(filename, content, directory)


class FileReadTool(BaseTool):
    """Tool for reading files from the generated code directory."""
    
    name: str = "file_read"
    description: str = "Read content from a file in the generated code directory"
    args_schema = FileReadInput
    
    def __init__(self):
        super().__init__()
        self.base_dir = Path("generated_code")
    
    def _run(self, filename: str, directory: str = "generated_code") -> str:
        """Read content from a file."""
        try:
            # Validate filename (security check)
            if ".." in filename or filename.startswith("/"):
                return "Error: Invalid filename for security reasons"
            
            file_path = Path(directory) / filename
            
            if not file_path.exists():
                return f"Error: File {file_path} does not exist"
            
            # Read file content
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            return f"Content of {file_path}:\n\n{content}"
        
        except Exception as e:
            return f"Error reading file: {str(e)}"
    
    async def _arun(self, filename: str, directory: str = "generated_code") -> str:
        """Async version of file read."""
        return self._run(filename, directory)


class CodeExecuteTool(BaseTool):
    """Tool for executing Python code safely."""
    
    name: str = "code_execute"
    description: str = "Execute Python code and return the output"
    args_schema = CodeExecuteInput
    
    def __init__(self):
        super().__init__()
        self.execution_dir = Path("generated_code")
        self.execution_dir.mkdir(exist_ok=True)
    
    def _run(self, code: str, filename: Optional[str] = None, timeout: int = 30) -> str:
        """Execute Python code."""
        try:
            # Create a temporary file for the code
            if filename:
                # Validate filename
                if ".." in filename or filename.startswith("/"):
                    return "Error: Invalid filename for security reasons"
                
                code_file = self.execution_dir / filename
            else:
                # Create temporary file
                with tempfile.NamedTemporaryFile(
                    mode='w', 
                    suffix='.py', 
                    dir=self.execution_dir, 
                    delete=False
                ) as f:
                    code_file = Path(f.name)
            
            # Write code to file
            with open(code_file, 'w', encoding='utf-8') as f:
                f.write(code)
            
            # Execute the code
            result = subprocess.run(
                ["python", str(code_file)],
                capture_output=True,
                text=True,
                timeout=timeout,
                cwd=self.execution_dir
            )
            
            # Format output
            output_parts = []
            
            if result.stdout:
                output_parts.append(f"**Output:**\n{result.stdout}")
            
            if result.stderr:
                output_parts.append(f"**Errors:**\n{result.stderr}")
            
            if result.returncode != 0:
                output_parts.append(f"**Exit Code:** {result.returncode}")
            
            # Clean up temporary file if it was created
            if not filename:
                try:
                    code_file.unlink()
                except:
                    pass
            
            if output_parts:
                return "\n\n".join(output_parts)
            else:
                return "Code executed successfully with no output"
        
        except subprocess.TimeoutExpired:
            return f"Error: Code execution timed out after {timeout} seconds"
        
        except Exception as e:
            return f"Error executing code: {str(e)}"
    
    async def _arun(self, code: str, filename: Optional[str] = None, timeout: int = 30) -> str:
        """Async version of code execution."""
        return self._run(code, filename, timeout)
    
    def _validate_code(self, code: str) -> bool:
        """Basic validation to prevent dangerous operations."""
        dangerous_patterns = [
            "import os",
            "import subprocess",
            "import sys",
            "__import__",
            "exec(",
            "eval(",
            "open(",
            "file(",
            "input(",
            "raw_input("
        ]
        
        code_lower = code.lower()
        for pattern in dangerous_patterns:
            if pattern in code_lower:
                return False
        
        return True
