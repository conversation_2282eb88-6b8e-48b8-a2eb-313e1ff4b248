"""Tests for GAAPF configuration."""

import pytest
import tempfile
from pathlib import Path
from datetime import datetime

from pyframeworks_assistant.config.user_profiles import (
    UserProfile, 
    SkillLevel, 
    LearningStyle, 
    LearningPace,
    create_user_profile
)
from pyframeworks_assistant.config.framework_configs import (
    SupportedFrameworks,
    get_framework_config,
    get_available_frameworks
)
from pyframeworks_assistant.config.system_config import SystemConfig


class TestUserProfiles:
    """Test user profile functionality."""
    
    def test_user_profile_creation(self):
        """Test creating a user profile."""
        profile = UserProfile(
            user_id="test_user",
            name="Test User",
            programming_experience_years=5,
            python_skill_level=SkillLevel.INTERMEDIATE,
            ai_ml_experience=SkillLevel.BEGINNER,
            preferred_learning_style=LearningStyle.HANDS_ON,
            learning_pace=LearningPace.MODERATE,
            learning_goals=["Learn LangChain", "Build applications"]
        )
        
        assert profile.user_id == "test_user"
        assert profile.name == "Test User"
        assert profile.programming_experience_years == 5
        assert profile.python_skill_level == SkillLevel.INTERMEDIATE
        assert profile.ai_ml_experience == SkillLevel.BEGINNER
        assert profile.preferred_learning_style == LearningStyle.HANDS_ON
        assert profile.learning_pace == LearningPace.MODERATE
        assert len(profile.learning_goals) == 2
    
    def test_user_profile_defaults(self):
        """Test user profile with default values."""
        profile = UserProfile(
            user_id="test_user",
            name="Test User"
        )
        
        assert profile.programming_experience_years == 0
        assert profile.python_skill_level == SkillLevel.BEGINNER
        assert profile.ai_ml_experience == SkillLevel.NONE
        assert profile.preferred_learning_style == LearningStyle.MIXED
        assert profile.learning_pace == LearningPace.MODERATE
        assert profile.learning_goals == []
        assert profile.total_learning_time_minutes == 0
        assert profile.current_streak_days == 0
    
    def test_get_experience_level(self):
        """Test experience level calculation."""
        # Beginner profile
        beginner = UserProfile(
            user_id="beginner",
            name="Beginner",
            programming_experience_years=1,
            python_skill_level=SkillLevel.BEGINNER,
            ai_ml_experience=SkillLevel.NONE
        )
        assert beginner.get_experience_level() == "beginner"
        
        # Intermediate profile
        intermediate = UserProfile(
            user_id="intermediate",
            name="Intermediate",
            programming_experience_years=3,
            python_skill_level=SkillLevel.INTERMEDIATE,
            ai_ml_experience=SkillLevel.BEGINNER
        )
        assert intermediate.get_experience_level() == "intermediate"
        
        # Advanced profile
        advanced = UserProfile(
            user_id="advanced",
            name="Advanced",
            programming_experience_years=8,
            python_skill_level=SkillLevel.ADVANCED,
            ai_ml_experience=SkillLevel.INTERMEDIATE
        )
        assert advanced.get_experience_level() == "advanced"
    
    def test_update_learning_time(self):
        """Test updating learning time."""
        profile = UserProfile(user_id="test", name="Test")
        
        initial_time = profile.total_learning_time_minutes
        profile.update_learning_time(30)
        
        assert profile.total_learning_time_minutes == initial_time + 30
        assert profile.last_updated > profile.created_at
    
    def test_add_completed_module(self):
        """Test adding completed modules."""
        profile = UserProfile(user_id="test", name="Test")
        
        profile.add_completed_module("langchain", "lc_basics")
        
        assert "langchain" in profile.completed_modules
        assert "lc_basics" in profile.completed_modules["langchain"]
        assert profile.last_updated > profile.created_at
    
    def test_save_and_load_profile(self, temp_dir):
        """Test saving and loading profiles."""
        profile = UserProfile(
            user_id="test_user",
            name="Test User",
            programming_experience_years=3,
            python_skill_level=SkillLevel.INTERMEDIATE,
            learning_goals=["Learn LangChain"]
        )
        
        # Save profile
        profile_file = temp_dir / "test_profile.json"
        profile.save_to_file(profile_file)
        
        assert profile_file.exists()
        
        # Load profile
        loaded_profile = UserProfile.load_from_file(profile_file)
        
        assert loaded_profile.user_id == profile.user_id
        assert loaded_profile.name == profile.name
        assert loaded_profile.programming_experience_years == profile.programming_experience_years
        assert loaded_profile.python_skill_level == profile.python_skill_level
        assert loaded_profile.learning_goals == profile.learning_goals
    
    def test_create_user_profile_interactive(self):
        """Test interactive user profile creation."""
        # This would require mocking input() for interactive testing
        # For now, just test that the function exists and is callable
        assert callable(create_user_profile)


class TestFrameworkConfigs:
    """Test framework configuration functionality."""
    
    def test_supported_frameworks(self):
        """Test supported frameworks enum."""
        assert SupportedFrameworks.LANGCHAIN.value == "langchain"
        assert SupportedFrameworks.LANGGRAPH.value == "langgraph"
    
    def test_get_framework_config(self):
        """Test getting framework configuration."""
        # Test LangChain config
        langchain_config = get_framework_config(SupportedFrameworks.LANGCHAIN)
        
        assert langchain_config.name == "LangChain"
        assert langchain_config.framework_id == "langchain"
        assert "LangChain is a framework" in langchain_config.description
        assert len(langchain_config.modules) > 0
        assert "lc_basics" in langchain_config.modules
        
        # Test LangGraph config
        langgraph_config = get_framework_config(SupportedFrameworks.LANGGRAPH)
        
        assert langgraph_config.name == "LangGraph"
        assert langgraph_config.framework_id == "langgraph"
        assert "LangGraph extends LangChain" in langgraph_config.description
        assert len(langgraph_config.modules) > 0
    
    def test_get_available_frameworks(self):
        """Test getting available frameworks."""
        frameworks = get_available_frameworks()
        
        assert isinstance(frameworks, list)
        assert SupportedFrameworks.LANGCHAIN in frameworks
        assert SupportedFrameworks.LANGGRAPH in frameworks
    
    def test_module_configurations(self):
        """Test module configurations."""
        langchain_config = get_framework_config(SupportedFrameworks.LANGCHAIN)
        
        # Test basic module
        basics_module = langchain_config.modules["lc_basics"]
        assert basics_module.title == "LangChain Basics"
        assert basics_module.difficulty_level >= 1
        assert basics_module.estimated_duration_minutes > 0
        assert isinstance(basics_module.learning_objectives, list)
        assert isinstance(basics_module.prerequisites, list)
        
        # Test chains module
        chains_module = langchain_config.modules["lc_chains"]
        assert chains_module.title == "Chains and Composition"
        assert "lc_basics" in chains_module.prerequisites


class TestSystemConfig:
    """Test system configuration."""
    
    def test_system_config_initialization(self):
        """Test system config initialization."""
        config = SystemConfig()
        
        # Test default values
        assert config.max_concurrent_agents == 16
        assert config.constellation_timeout_seconds == 300
        assert config.user_profiles_dir == "user_profiles"
        assert config.debug == False
    
    def test_has_llm_api_key(self):
        """Test LLM API key detection."""
        config = SystemConfig()
        
        # This will depend on actual environment variables
        # In a real test, you might want to mock the environment
        has_key = config.has_llm_api_key()
        assert isinstance(has_key, bool)
    
    def test_get_primary_llm_provider(self):
        """Test getting primary LLM provider."""
        config = SystemConfig()
        
        provider = config.get_primary_llm_provider()
        # Should return a string or None
        assert provider is None or isinstance(provider, str)
    
    def test_environment_variable_loading(self):
        """Test environment variable loading."""
        import os
        
        # Test with mock environment variables
        test_env = {
            "GOOGLE_API_KEY": "test_google_key",
            "GAAPF_MAX_CONCURRENT_AGENTS": "8",
            "GAAPF_DEBUG": "true"
        }
        
        with pytest.MonkeyPatch().context() as m:
            for key, value in test_env.items():
                m.setenv(key, value)
            
            config = SystemConfig()
            
            assert config.google_api_key == "test_google_key"
            assert config.max_concurrent_agents == 8
            assert config.debug == True


class TestSkillLevels:
    """Test skill level enums."""
    
    def test_skill_level_values(self):
        """Test skill level enum values."""
        assert SkillLevel.NONE.value == "none"
        assert SkillLevel.BEGINNER.value == "beginner"
        assert SkillLevel.INTERMEDIATE.value == "intermediate"
        assert SkillLevel.ADVANCED.value == "advanced"
        assert SkillLevel.EXPERT.value == "expert"
    
    def test_learning_style_values(self):
        """Test learning style enum values."""
        assert LearningStyle.VISUAL.value == "visual"
        assert LearningStyle.HANDS_ON.value == "hands_on"
        assert LearningStyle.THEORETICAL.value == "theoretical"
        assert LearningStyle.MIXED.value == "mixed"
    
    def test_learning_pace_values(self):
        """Test learning pace enum values."""
        assert LearningPace.SLOW.value == "slow"
        assert LearningPace.MODERATE.value == "moderate"
        assert LearningPace.FAST.value == "fast"
        assert LearningPace.INTENSIVE.value == "intensive"
