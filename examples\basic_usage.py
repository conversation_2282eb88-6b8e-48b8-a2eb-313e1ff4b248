"""
Basic usage example for GAAPF system.

This example demonstrates how to:
1. Set up the GAAPF system
2. Create a user profile
3. Start a learning session
4. Interact with agents
5. End the session and get analytics
"""

import asyncio
import os
from pathlib import Path

# Add the src directory to the path for local development
import sys
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from pyframeworks_assistant import (
    LearningHubCore,
    UserProfile,
    SupportedFrameworks,
    SkillLevel,
    LearningStyle,
    LearningPace,
    get_system_info
)
from pyframeworks_assistant.interfaces.cli.llm_setup import setup_llm


async def main():
    """Main example function."""
    print("🤖 GAAPF Basic Usage Example")
    print("=" * 50)
    
    # Check system info
    print("\n📊 System Information:")
    system_info = get_system_info()
    for key, value in system_info.items():
        print(f"  {key}: {value}")
    
    # Setup LLM
    print("\n🔧 Setting up LLM...")
    llm = setup_llm()
    if not llm:
        print("❌ No LLM provider available. Please set API keys in .env file.")
        return
    
    # Initialize learning hub
    print("\n🚀 Initializing GAAPF Learning Hub...")
    hub = LearningHubCore(llm)
    await hub.initialize()
    print("✅ Learning Hub initialized successfully!")
    
    # Create user profile
    print("\n👤 Creating User Profile...")
    profile = UserProfile(
        user_id="example_user",
        name="Example User",
        programming_experience_years=3,
        python_skill_level=SkillLevel.INTERMEDIATE,
        ai_ml_experience=SkillLevel.BEGINNER,
        preferred_learning_style=LearningStyle.HANDS_ON,
        learning_pace=LearningPace.MODERATE,
        learning_goals=[
            "Learn LangChain fundamentals",
            "Build a RAG application",
            "Understand agent workflows"
        ]
    )
    print(f"✅ Created profile for {profile.name}")
    print(f"   Experience level: {profile.get_experience_level()}")
    print(f"   Learning style: {profile.preferred_learning_style.value}")
    print(f"   Goals: {', '.join(profile.learning_goals)}")
    
    # Start learning session
    print("\n🎓 Starting Learning Session...")
    session_id, session_info = await hub.start_learning_session(
        user_profile=profile,
        framework=SupportedFrameworks.LANGCHAIN,
        module_id="lc_basics"
    )
    
    print(f"✅ Session started: {session_id}")
    print(f"   Constellation: {session_info['constellation_type']}")
    print(f"   Module: {session_info['module_title']}")
    print(f"   Estimated duration: {session_info['estimated_duration']} minutes")
    print(f"   Constellation confidence: {session_info.get('constellation_confidence', 0):.1%}")
    
    # Example interactions
    print("\n💬 Example Learning Interactions:")
    print("-" * 40)
    
    # Interaction 1: Basic question
    print("\n🧑 User: What is LangChain and why should I use it?")
    result1 = await hub.process_user_message(
        session_id=session_id,
        user_message="What is LangChain and why should I use it?"
    )
    
    print(f"🤖 {result1['agent_role'].replace('_', ' ').title()}: {result1['response'][:200]}...")
    print(f"   Confidence: {result1.get('confidence', 0):.1%}")
    if result1.get('handoff_suggestion'):
        print(f"   💡 Suggested next agent: {result1['handoff_suggestion']}")
    
    # Interaction 2: Code request
    print("\n🧑 User: Can you show me a simple LangChain example with code?")
    result2 = await hub.process_user_message(
        session_id=session_id,
        user_message="Can you show me a simple LangChain example with code?"
    )
    
    print(f"🤖 {result2['agent_role'].replace('_', ' ').title()}: {result2['response'][:200]}...")
    print(f"   Confidence: {result2.get('confidence', 0):.1%}")
    if result2.get('handoff_suggestion'):
        print(f"   💡 Suggested next agent: {result2['handoff_suggestion']}")
    
    # Interaction 3: Practice request
    print("\n🧑 User: I want to practice building chains. Can you give me an exercise?")
    result3 = await hub.process_user_message(
        session_id=session_id,
        user_message="I want to practice building chains. Can you give me an exercise?"
    )
    
    print(f"🤖 {result3['agent_role'].replace('_', ' ').title()}: {result3['response'][:200]}...")
    print(f"   Confidence: {result3.get('confidence', 0):.1%}")
    if result3.get('handoff_suggestion'):
        print(f"   💡 Suggested next agent: {result3['handoff_suggestion']}")
    
    # Get session status
    print("\n📊 Session Status:")
    status = hub.get_session_status(session_id)
    if status:
        print(f"   Duration: {status['duration_minutes']:.1f} minutes")
        print(f"   Interactions: {status['interactions_count']}")
        print(f"   Framework: {status['framework']}")
        print(f"   Module: {status['module_id']}")
    
    # End session
    print("\n🏁 Ending Learning Session...")
    summary = await hub.end_learning_session(
        session_id=session_id,
        user_feedback={
            "satisfaction_score": 0.9,
            "user_comments": "Great learning experience!"
        }
    )
    
    print("✅ Session completed!")
    print(f"   Duration: {summary['duration_minutes']:.1f} minutes")
    print(f"   Total interactions: {summary['interactions_count']}")
    print(f"   Constellation used: {summary['constellation_type']}")
    print(f"   Completion estimate: {summary.get('completion_estimate', 0):.1%}")
    
    if summary.get('next_recommendations'):
        print("   📚 Next recommendations:")
        for rec in summary['next_recommendations']:
            print(f"     • {rec}")
    
    print("\n🎉 Example completed successfully!")
    print("\nTo run the full CLI interface, use: gaapf-cli")
    print("To run the Streamlit demo, use: streamlit run src/pyframeworks_assistant/interfaces/streamlit_demo.py")


if __name__ == "__main__":
    # Check for environment file
    env_file = Path(__file__).parent.parent / ".env"
    if not env_file.exists():
        print("⚠️  No .env file found. Please copy env.example to .env and add your API keys.")
        print("   Example: cp env.example .env")
        print("   Then edit .env to add your LLM API keys.")
        exit(1)
    
    # Check for API keys
    has_google = bool(os.getenv("GOOGLE_API_KEY"))
    has_openai = bool(os.getenv("OPENAI_API_KEY"))
    has_anthropic = bool(os.getenv("ANTHROPIC_API_KEY"))
    
    if not (has_google or has_openai or has_anthropic):
        print("❌ No LLM API keys found in environment variables.")
        print("   Please set at least one of:")
        print("   • GOOGLE_API_KEY (for Google Gemini)")
        print("   • OPENAI_API_KEY (for OpenAI GPT)")
        print("   • ANTHROPIC_API_KEY (for Anthropic Claude)")
        exit(1)
    
    # Run the example
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n👋 Example interrupted by user. Goodbye!")
    except Exception as e:
        print(f"\n❌ Error running example: {e}")
        print("Please check your API keys and try again.")
