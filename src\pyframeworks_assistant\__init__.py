"""
GAAPF - Guidance AI Agent for Python Framework

An Adaptive Multi-Agent Learning System for AI Framework Education
Built with LangChain, LangGraph, and advanced temporal optimization algorithms.
"""

__version__ = "1.0.0"
__author__ = "GAAPF Development Team"
__email__ = "<EMAIL>"
__description__ = "Guidance AI Agent for Python Framework - Adaptive Multi-Agent Learning System"

# Core imports
from .core.learning_hub import LearningHubCore
from .core.constellation import ConstellationManager
from .core.constellation_types import ConstellationType, get_constellation_config
from .core.temporal_state import TemporalStateManager
from .core.knowledge_graph import KnowledgeGraphManager
from .core.analytics_engine import AnalyticsEngine

# Configuration imports
from .config.user_profiles import UserProfile, SkillLevel, LearningStyle, LearningPace
from .config.framework_configs import SupportedFrameworks, get_framework_config
from .config.system_config import system_config

# Agent imports
from .agents.base_agent import BaseAgent, Agent<PERSON>ole, AgentDomain
from .agents.agent_factory import AgentFactory

# Memory imports
from .memory.memory_manager import MemoryManager

# Tools imports
from .tools.tool_manager import ToolManager

__all__ = [
    # Core classes
    "LearningHubCore",
    "ConstellationManager",
    "ConstellationType",
    "get_constellation_config",
    "TemporalStateManager",
    "KnowledgeGraphManager",
    "AnalyticsEngine",

    # Configuration
    "UserProfile",
    "SkillLevel",
    "LearningStyle",
    "LearningPace",
    "SupportedFrameworks",
    "get_framework_config",
    "system_config",

    # Agents
    "BaseAgent",
    "AgentRole",
    "AgentDomain",
    "AgentFactory",

    # Memory and Tools
    "MemoryManager",
    "ToolManager",

    # Metadata
    "__version__",
    "__author__",
    "__email__",
    "__description__",
]


def get_version() -> str:
    """Get the current version of GAAPF."""
    return __version__


def get_system_info() -> dict:
    """Get system information and status."""
    return {
        "version": __version__,
        "description": __description__,
        "author": __author__,
        "has_llm_api_key": system_config.has_llm_api_key(),
        "primary_llm_provider": system_config.get_primary_llm_provider(),
        "supported_frameworks": [f.value for f in SupportedFrameworks],
        "available_constellations": [c.value for c in ConstellationType],
    }


# Package-level configuration
import warnings
import logging

# Configure logging
logging.getLogger(__name__).addHandler(logging.NullHandler())

# Filter out common warnings
warnings.filterwarnings("ignore", category=UserWarning, module="langchain")
warnings.filterwarnings("ignore", category=DeprecationWarning, module="pydantic")

# Startup message
if system_config.debug:
    print(f"🤖 GAAPF v{__version__} - Debug mode enabled")
    print(f"📊 System info: {get_system_info()}")
