"""Knowledge memory for concept mastery tracking."""

import json
import asyncio
from typing import Dict, List, Optional, Any, Set
from datetime import datetime, timedelta
from pathlib import Path
from pydantic import BaseModel, Field
from collections import defaultdict


class ConceptExposure(BaseModel):
    """Tracks user's exposure to a concept."""
    
    concept: str
    first_exposure: datetime
    last_exposure: datetime
    exposure_count: int = 1
    contexts: List[str] = Field(default_factory=list)  # Contexts where concept appeared
    
    def update_exposure(self, context: str, timestamp: Optional[datetime] = None) -> None:
        """Update exposure information."""
        self.last_exposure = timestamp or datetime.now()
        self.exposure_count += 1
        if context not in self.contexts:
            self.contexts.append(context)


class ConceptMastery(BaseModel):
    """Tracks user's mastery of a concept."""
    
    concept: str
    mastery_level: float = Field(0.0, ge=0.0, le=1.0)
    confidence: float = Field(0.0, ge=0.0, le=1.0)
    
    # Learning progression
    learning_sessions: List[str] = Field(default_factory=list)
    practice_count: int = 0
    assessment_scores: List[float] = Field(default_factory=list)
    
    # Temporal tracking
    first_learned: Optional[datetime] = None
    last_practiced: Optional[datetime] = None
    
    def update_mastery(
        self,
        session_id: str,
        practice_score: Optional[float] = None,
        assessment_score: Optional[float] = None
    ) -> None:
        """Update mastery information."""
        timestamp = datetime.now()
        
        if session_id not in self.learning_sessions:
            self.learning_sessions.append(session_id)
        
        if practice_score is not None:
            self.practice_count += 1
            self.last_practiced = timestamp
            
            # Update mastery level with exponential moving average
            alpha = 0.3  # Learning rate
            self.mastery_level = alpha * practice_score + (1 - alpha) * self.mastery_level
        
        if assessment_score is not None:
            self.assessment_scores.append(assessment_score)
            
            # Update confidence based on assessment consistency
            if len(self.assessment_scores) >= 2:
                recent_scores = self.assessment_scores[-3:]  # Last 3 assessments
                score_variance = sum((s - sum(recent_scores)/len(recent_scores))**2 for s in recent_scores) / len(recent_scores)
                self.confidence = max(0.0, 1.0 - score_variance)
        
        if self.first_learned is None and self.mastery_level > 0.3:
            self.first_learned = timestamp


class KnowledgeMemory:
    """Manages knowledge and concept mastery memory."""
    
    def __init__(self, data_dir: Path):
        """Initialize knowledge memory.
        
        Args:
            data_dir: Directory for storing knowledge data
        """
        self.data_dir = data_dir
        self.data_dir.mkdir(exist_ok=True)
        
        # User knowledge tracking
        self.user_exposures: Dict[str, Dict[str, ConceptExposure]] = defaultdict(dict)
        self.user_mastery: Dict[str, Dict[str, ConceptMastery]] = defaultdict(dict)
        
        # Concept relationships and prerequisites
        self.concept_prerequisites: Dict[str, List[str]] = {}
        self.concept_relationships: Dict[str, List[str]] = defaultdict(list)
        
        self.is_initialized = False
    
    async def initialize(self) -> None:
        """Initialize knowledge memory system."""
        if self.is_initialized:
            return
        
        # Load existing knowledge data
        await self._load_knowledge_data()
        
        # Initialize concept relationships
        await self._initialize_concept_relationships()
        
        self.is_initialized = True
    
    async def _load_knowledge_data(self) -> None:
        """Load knowledge data from storage."""
        try:
            # Load exposures
            exposures_file = self.data_dir / "concept_exposures.json"
            if exposures_file.exists():
                with open(exposures_file, 'r') as f:
                    exposures_data = json.load(f)
                
                for user_id, user_exposures in exposures_data.items():
                    for concept, exposure_data in user_exposures.items():
                        exposure = ConceptExposure(**exposure_data)
                        self.user_exposures[user_id][concept] = exposure
            
            # Load mastery
            mastery_file = self.data_dir / "concept_mastery.json"
            if mastery_file.exists():
                with open(mastery_file, 'r') as f:
                    mastery_data = json.load(f)
                
                for user_id, user_mastery in mastery_data.items():
                    for concept, mastery_info in user_mastery.items():
                        mastery = ConceptMastery(**mastery_info)
                        self.user_mastery[user_id][concept] = mastery
        
        except Exception as e:
            print(f"Warning: Could not load knowledge data: {e}")
    
    async def _initialize_concept_relationships(self) -> None:
        """Initialize concept relationships and prerequisites."""
        # Basic LangChain concept relationships
        self.concept_prerequisites = {
            "chain": ["llm", "prompt"],
            "agent": ["chain", "tool"],
            "memory": ["chain"],
            "rag": ["vector", "embedding", "chain"],
            "vector": ["embedding"],
            "langgraph": ["langchain", "agent"]
        }
        
        # Build bidirectional relationships
        for concept, prereqs in self.concept_prerequisites.items():
            for prereq in prereqs:
                self.concept_relationships[prereq].append(concept)
                self.concept_relationships[concept].append(prereq)
    
    async def update_concept_exposure(
        self,
        user_id: str,
        concepts: List[str],
        timestamp: Optional[datetime] = None,
        context: str = "learning_session"
    ) -> None:
        """Update user's exposure to concepts.
        
        Args:
            user_id: User identifier
            concepts: List of concepts encountered
            timestamp: Exposure timestamp
            context: Context of exposure
        """
        timestamp = timestamp or datetime.now()
        
        for concept in concepts:
            concept_lower = concept.lower()
            
            if concept_lower in self.user_exposures[user_id]:
                # Update existing exposure
                self.user_exposures[user_id][concept_lower].update_exposure(context, timestamp)
            else:
                # Create new exposure
                exposure = ConceptExposure(
                    concept=concept_lower,
                    first_exposure=timestamp,
                    last_exposure=timestamp,
                    contexts=[context]
                )
                self.user_exposures[user_id][concept_lower] = exposure
        
        # Persist changes
        await self._persist_knowledge_data()
    
    async def update_concept_mastery(
        self,
        user_id: str,
        concepts: List[str],
        mastery_scores: Dict[str, float],
        session_id: Optional[str] = None
    ) -> None:
        """Update user's concept mastery.
        
        Args:
            user_id: User identifier
            concepts: List of concepts practiced
            mastery_scores: Mastery scores for concepts
            session_id: Session identifier
        """
        session_id = session_id or f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        for concept in concepts:
            concept_lower = concept.lower()
            mastery_score = mastery_scores.get(concept_lower, 0.5)
            
            if concept_lower in self.user_mastery[user_id]:
                # Update existing mastery
                self.user_mastery[user_id][concept_lower].update_mastery(
                    session_id, practice_score=mastery_score
                )
            else:
                # Create new mastery record
                mastery = ConceptMastery(
                    concept=concept_lower,
                    mastery_level=mastery_score,
                    learning_sessions=[session_id],
                    practice_count=1,
                    first_learned=datetime.now(),
                    last_practiced=datetime.now()
                )
                self.user_mastery[user_id][concept_lower] = mastery
        
        # Persist changes
        await self._persist_knowledge_data()
    
    async def get_concept_context(
        self,
        user_id: str,
        concepts: List[str]
    ) -> Dict[str, Any]:
        """Get context about user's knowledge of concepts.
        
        Args:
            user_id: User identifier
            concepts: Concepts to get context for
            
        Returns:
            Context information about concepts
        """
        context = {
            "user_id": user_id,
            "concepts": {},
            "knowledge_gaps": [],
            "mastery_summary": {}
        }
        
        for concept in concepts:
            concept_lower = concept.lower()
            
            # Get exposure information
            exposure = self.user_exposures[user_id].get(concept_lower)
            mastery = self.user_mastery[user_id].get(concept_lower)
            
            concept_info = {
                "concept": concept_lower,
                "exposed": exposure is not None,
                "mastery_level": mastery.mastery_level if mastery else 0.0,
                "confidence": mastery.confidence if mastery else 0.0,
                "practice_count": mastery.practice_count if mastery else 0
            }
            
            if exposure:
                concept_info.update({
                    "first_exposure": exposure.first_exposure.isoformat(),
                    "last_exposure": exposure.last_exposure.isoformat(),
                    "exposure_count": exposure.exposure_count,
                    "contexts": exposure.contexts
                })
            
            context["concepts"][concept_lower] = concept_info
            
            # Check for knowledge gaps
            if concept_lower in self.concept_prerequisites:
                for prereq in self.concept_prerequisites[concept_lower]:
                    prereq_mastery = self.user_mastery[user_id].get(prereq)
                    if not prereq_mastery or prereq_mastery.mastery_level < 0.5:
                        context["knowledge_gaps"].append({
                            "concept": concept_lower,
                            "missing_prerequisite": prereq,
                            "recommendation": f"Learn {prereq} before advancing to {concept_lower}"
                        })
        
        # Calculate mastery summary
        all_mastery = list(self.user_mastery[user_id].values())
        if all_mastery:
            context["mastery_summary"] = {
                "total_concepts": len(all_mastery),
                "average_mastery": sum(m.mastery_level for m in all_mastery) / len(all_mastery),
                "concepts_mastered": len([m for m in all_mastery if m.mastery_level > 0.8]),
                "concepts_learning": len([m for m in all_mastery if 0.3 <= m.mastery_level <= 0.8]),
                "concepts_struggling": len([m for m in all_mastery if m.mastery_level < 0.3])
            }
        
        return context
    
    async def get_learning_recommendations(self, user_id: str) -> List[Dict[str, Any]]:
        """Get learning recommendations based on knowledge state.
        
        Args:
            user_id: User identifier
            
        Returns:
            List of learning recommendations
        """
        recommendations = []
        user_mastery = self.user_mastery[user_id]
        
        # Find concepts ready to learn (prerequisites met)
        for concept, prereqs in self.concept_prerequisites.items():
            if concept not in user_mastery or user_mastery[concept].mastery_level < 0.5:
                # Check if prerequisites are met
                prereqs_met = all(
                    prereq in user_mastery and user_mastery[prereq].mastery_level >= 0.6
                    for prereq in prereqs
                )
                
                if prereqs_met:
                    recommendations.append({
                        "type": "ready_to_learn",
                        "concept": concept,
                        "reason": f"Prerequisites met: {', '.join(prereqs)}",
                        "priority": "high"
                    })
        
        # Find concepts that need reinforcement
        for concept, mastery in user_mastery.items():
            if 0.3 <= mastery.mastery_level < 0.6:
                recommendations.append({
                    "type": "needs_practice",
                    "concept": concept,
                    "reason": f"Mastery level: {mastery.mastery_level:.2f}",
                    "priority": "medium"
                })
        
        # Find concepts that haven't been practiced recently
        cutoff_date = datetime.now() - timedelta(days=7)
        for concept, mastery in user_mastery.items():
            if (mastery.last_practiced and 
                mastery.last_practiced < cutoff_date and 
                mastery.mastery_level > 0.5):
                recommendations.append({
                    "type": "review_needed",
                    "concept": concept,
                    "reason": f"Last practiced: {mastery.last_practiced.strftime('%Y-%m-%d')}",
                    "priority": "low"
                })
        
        # Sort by priority
        priority_order = {"high": 3, "medium": 2, "low": 1}
        recommendations.sort(key=lambda x: priority_order[x["priority"]], reverse=True)
        
        return recommendations[:10]  # Return top 10 recommendations
    
    async def get_user_summary(self, user_id: str) -> Dict[str, Any]:
        """Get knowledge memory summary for a user."""
        exposures = self.user_exposures[user_id]
        mastery = self.user_mastery[user_id]
        
        return {
            "system": "knowledge_memory",
            "concepts_exposed": len(exposures),
            "concepts_learning": len(mastery),
            "average_mastery": (
                sum(m.mastery_level for m in mastery.values()) / len(mastery)
                if mastery else 0.0
            ),
            "concepts_mastered": len([m for m in mastery.values() if m.mastery_level > 0.8]),
            "total_practice_sessions": sum(m.practice_count for m in mastery.values()),
            "knowledge_gaps": len(await self.get_learning_recommendations(user_id))
        }
    
    async def _persist_knowledge_data(self) -> None:
        """Persist knowledge data to storage."""
        try:
            # Save exposures
            exposures_data = {}
            for user_id, user_exposures in self.user_exposures.items():
                exposures_data[user_id] = {
                    concept: exposure.dict() for concept, exposure in user_exposures.items()
                }
            
            with open(self.data_dir / "concept_exposures.json", 'w') as f:
                json.dump(exposures_data, f, indent=2, default=str)
            
            # Save mastery
            mastery_data = {}
            for user_id, user_mastery in self.user_mastery.items():
                mastery_data[user_id] = {
                    concept: mastery.dict() for concept, mastery in user_mastery.items()
                }
            
            with open(self.data_dir / "concept_mastery.json", 'w') as f:
                json.dump(mastery_data, f, indent=2, default=str)
        
        except Exception as e:
            print(f"Warning: Could not persist knowledge data: {e}")
    
    async def cleanup_old_data(self, cutoff_date: datetime) -> None:
        """Clean up old knowledge data.
        
        Args:
            cutoff_date: Date before which to remove data
        """
        # Remove old exposures
        for user_id in list(self.user_exposures.keys()):
            user_exposures = self.user_exposures[user_id]
            to_remove = []
            
            for concept, exposure in user_exposures.items():
                if exposure.last_exposure < cutoff_date:
                    to_remove.append(concept)
            
            for concept in to_remove:
                del user_exposures[concept]
            
            if not user_exposures:
                del self.user_exposures[user_id]
        
        # Clean up old mastery data (keep mastery but remove old session references)
        for user_id in self.user_mastery:
            for concept, mastery in self.user_mastery[user_id].items():
                if mastery.last_practiced and mastery.last_practiced < cutoff_date:
                    # Keep mastery level but clear old session data
                    mastery.learning_sessions = []
                    mastery.assessment_scores = mastery.assessment_scores[-5:]  # Keep last 5 scores
