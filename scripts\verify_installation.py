#!/usr/bin/env python3
"""
GAAPF Installation Verification Script

This script verifies that GAAPF is properly installed and configured.
"""

import sys
import os
import asyncio
import importlib
from pathlib import Path
from typing import List, Tuple, Optional


class VerificationResult:
    """Represents the result of a verification check."""
    
    def __init__(self, name: str, success: bool, message: str, details: Optional[str] = None):
        self.name = name
        self.success = success
        self.message = message
        self.details = details


class GAAPFVerifier:
    """Verifies GAAPF installation and configuration."""
    
    def __init__(self):
        self.results: List[VerificationResult] = []
    
    def add_result(self, name: str, success: bool, message: str, details: Optional[str] = None):
        """Add a verification result."""
        self.results.append(VerificationResult(name, success, message, details))
    
    def check_python_version(self) -> bool:
        """Check Python version compatibility."""
        try:
            if sys.version_info >= (3, 10):
                self.add_result(
                    "Python Version",
                    True,
                    f"✅ Python {sys.version.split()[0]} (compatible)"
                )
                return True
            else:
                self.add_result(
                    "Python Version",
                    False,
                    f"❌ Python {sys.version.split()[0]} (requires 3.10+)"
                )
                return False
        except Exception as e:
            self.add_result("Python Version", False, f"❌ Error checking Python version: {e}")
            return False
    
    def check_core_imports(self) -> bool:
        """Check if core GAAPF modules can be imported."""
        core_modules = [
            ("pyframeworks_assistant", "Main package"),
            ("pyframeworks_assistant.config.user_profiles", "User profiles"),
            ("pyframeworks_assistant.config.framework_configs", "Framework configs"),
            ("pyframeworks_assistant.core.learning_hub", "Learning hub"),
            ("pyframeworks_assistant.core.constellation", "Constellation manager"),
            ("pyframeworks_assistant.agents.agent_factory", "Agent factory"),
            ("pyframeworks_assistant.memory.memory_manager", "Memory manager"),
            ("pyframeworks_assistant.tools.tool_manager", "Tool manager"),
        ]
        
        all_success = True
        
        for module_name, description in core_modules:
            try:
                importlib.import_module(module_name)
                self.add_result(
                    f"Import {description}",
                    True,
                    f"✅ {module_name}"
                )
            except ImportError as e:
                self.add_result(
                    f"Import {description}",
                    False,
                    f"❌ {module_name}: {e}"
                )
                all_success = False
            except Exception as e:
                self.add_result(
                    f"Import {description}",
                    False,
                    f"❌ {module_name}: Unexpected error: {e}"
                )
                all_success = False
        
        return all_success
    
    def check_dependencies(self) -> bool:
        """Check if required dependencies are available."""
        dependencies = [
            ("langchain", "LangChain core"),
            ("langchain_core", "LangChain core"),
            ("langgraph", "LangGraph"),
            ("pydantic", "Pydantic"),
            ("rich", "Rich CLI"),
            ("click", "Click CLI"),
            ("streamlit", "Streamlit"),
            ("numpy", "NumPy"),
            ("requests", "Requests"),
        ]
        
        all_success = True
        
        for module_name, description in dependencies:
            try:
                module = importlib.import_module(module_name)
                version = getattr(module, "__version__", "unknown")
                self.add_result(
                    f"Dependency {description}",
                    True,
                    f"✅ {module_name} v{version}"
                )
            except ImportError:
                self.add_result(
                    f"Dependency {description}",
                    False,
                    f"❌ {module_name} not found"
                )
                all_success = False
            except Exception as e:
                self.add_result(
                    f"Dependency {description}",
                    False,
                    f"❌ {module_name}: {e}"
                )
                all_success = False
        
        return all_success
    
    def check_optional_dependencies(self) -> bool:
        """Check optional dependencies for LLM providers."""
        optional_deps = [
            ("langchain_google_genai", "Google Gemini"),
            ("langchain_openai", "OpenAI"),
            ("langchain_anthropic", "Anthropic"),
            ("tavily", "Tavily Search"),
        ]
        
        available_count = 0
        
        for module_name, description in optional_deps:
            try:
                importlib.import_module(module_name)
                self.add_result(
                    f"Optional {description}",
                    True,
                    f"✅ {module_name} available"
                )
                available_count += 1
            except ImportError:
                self.add_result(
                    f"Optional {description}",
                    False,
                    f"⚠️  {module_name} not available"
                )
        
        return available_count > 0
    
    def check_environment_config(self) -> bool:
        """Check environment configuration."""
        try:
            from pyframeworks_assistant.config.system_config import system_config
            
            # Check for .env file
            env_file = Path(".env")
            if env_file.exists():
                self.add_result(
                    "Environment File",
                    True,
                    "✅ .env file found"
                )
            else:
                self.add_result(
                    "Environment File",
                    False,
                    "⚠️  .env file not found"
                )
            
            # Check API keys
            has_llm_key = system_config.has_llm_api_key()
            if has_llm_key:
                provider = system_config.get_primary_llm_provider()
                self.add_result(
                    "LLM API Keys",
                    True,
                    f"✅ LLM API key found (provider: {provider})"
                )
            else:
                self.add_result(
                    "LLM API Keys",
                    False,
                    "❌ No LLM API keys found"
                )
            
            return has_llm_key
        
        except Exception as e:
            self.add_result(
                "Environment Config",
                False,
                f"❌ Error checking config: {e}"
            )
            return False
    
    def check_llm_setup(self) -> bool:
        """Check if LLM can be set up."""
        try:
            from pyframeworks_assistant.interfaces.cli.llm_setup import setup_llm, get_available_providers
            
            # Check available providers
            providers = get_available_providers()
            if providers:
                self.add_result(
                    "LLM Providers",
                    True,
                    f"✅ Available providers: {', '.join(providers)}"
                )
            else:
                self.add_result(
                    "LLM Providers",
                    False,
                    "❌ No LLM providers available"
                )
                return False
            
            # Try to setup LLM
            llm = setup_llm()
            if llm:
                self.add_result(
                    "LLM Setup",
                    True,
                    f"✅ LLM setup successful: {type(llm).__name__}"
                )
                return True
            else:
                self.add_result(
                    "LLM Setup",
                    False,
                    "❌ LLM setup failed"
                )
                return False
        
        except Exception as e:
            self.add_result(
                "LLM Setup",
                False,
                f"❌ Error setting up LLM: {e}"
            )
            return False
    
    async def check_learning_hub(self) -> bool:
        """Check if learning hub can be initialized."""
        try:
            from pyframeworks_assistant.core.learning_hub import LearningHubCore
            from pyframeworks_assistant.interfaces.cli.llm_setup import setup_llm
            
            llm = setup_llm()
            if not llm:
                self.add_result(
                    "Learning Hub",
                    False,
                    "❌ Cannot test learning hub without LLM"
                )
                return False
            
            hub = LearningHubCore(llm)
            await hub.initialize()
            
            self.add_result(
                "Learning Hub",
                True,
                "✅ Learning hub initialized successfully"
            )
            return True
        
        except Exception as e:
            self.add_result(
                "Learning Hub",
                False,
                f"❌ Learning hub initialization failed: {e}"
            )
            return False
    
    def check_cli_entry_point(self) -> bool:
        """Check if CLI entry point works."""
        try:
            import subprocess
            result = subprocess.run([
                sys.executable, "-m", "pyframeworks_assistant.interfaces.cli.main", "--help"
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0 and "GAAPF" in result.stdout:
                self.add_result(
                    "CLI Entry Point",
                    True,
                    "✅ CLI entry point working"
                )
                return True
            else:
                self.add_result(
                    "CLI Entry Point",
                    False,
                    f"❌ CLI failed: {result.stderr}"
                )
                return False
        
        except Exception as e:
            self.add_result(
                "CLI Entry Point",
                False,
                f"❌ Error testing CLI: {e}"
            )
            return False
    
    async def run_all_checks(self) -> bool:
        """Run all verification checks."""
        print("🔍 GAAPF Installation Verification")
        print("=" * 50)
        
        checks = [
            ("Python Version", self.check_python_version),
            ("Core Imports", self.check_core_imports),
            ("Dependencies", self.check_dependencies),
            ("Optional Dependencies", self.check_optional_dependencies),
            ("Environment Config", self.check_environment_config),
            ("LLM Setup", self.check_llm_setup),
            ("Learning Hub", self.check_learning_hub),
            ("CLI Entry Point", self.check_cli_entry_point),
        ]
        
        all_critical_passed = True
        
        for check_name, check_func in checks:
            print(f"\n🔧 Running {check_name} check...")
            try:
                if asyncio.iscoroutinefunction(check_func):
                    result = await check_func()
                else:
                    result = check_func()
                
                if not result and check_name in ["Python Version", "Core Imports", "Dependencies"]:
                    all_critical_passed = False
            
            except Exception as e:
                self.add_result(check_name, False, f"❌ Check failed: {e}")
                if check_name in ["Python Version", "Core Imports", "Dependencies"]:
                    all_critical_passed = False
        
        return all_critical_passed
    
    def print_summary(self):
        """Print verification summary."""
        print("\n" + "=" * 50)
        print("📊 Verification Summary")
        print("=" * 50)
        
        success_count = sum(1 for r in self.results if r.success)
        total_count = len(self.results)
        
        for result in self.results:
            print(result.message)
            if result.details:
                print(f"   {result.details}")
        
        print(f"\n📈 Results: {success_count}/{total_count} checks passed")
        
        if success_count == total_count:
            print("\n🎉 All checks passed! GAAPF is ready to use.")
            print("\n🚀 Next steps:")
            print("   • Run: python examples/basic_usage.py")
            print("   • Or: gaapf-cli")
            print("   • Or: streamlit run src/pyframeworks_assistant/interfaces/streamlit_demo.py")
        elif success_count >= total_count * 0.8:
            print("\n✅ Most checks passed! GAAPF should work with minor issues.")
            print("   Check the failed items above for potential improvements.")
        else:
            print("\n❌ Several checks failed. Please review the issues above.")
            print("   Consider running the setup script: python scripts/setup.py")


async def main():
    """Main verification function."""
    verifier = GAAPFVerifier()
    
    try:
        success = await verifier.run_all_checks()
        verifier.print_summary()
        return success
    
    except KeyboardInterrupt:
        print("\n\n👋 Verification interrupted by user")
        return False
    
    except Exception as e:
        print(f"\n❌ Unexpected error during verification: {e}")
        return False


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ Failed to run verification: {e}")
        sys.exit(1)
