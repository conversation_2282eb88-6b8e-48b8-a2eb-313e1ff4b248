#!/usr/bin/env python3
"""Minimal test to check basic imports."""

import sys
from pathlib import Path

# Add src to path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

print("🧪 Testing minimal imports...")

try:
    print("1. Testing pydantic-settings...")
    from pydantic_settings import BaseSettings
    from pydantic import Field
    print("✅ pydantic-settings imported")
    
    print("2. Testing SystemConfig...")
    from pyframeworks_assistant.config.system_config import SystemConfig
    print("✅ SystemConfig imported")
    
    print("3. Creating SystemConfig instance...")
    config = SystemConfig()
    print(f"✅ SystemConfig created - log_level: {config.log_level}")
    
    print("4. Testing global config...")
    from pyframeworks_assistant.config.system_config import system_config
    print(f"✅ Global config - debug: {system_config.debug}")
    
    print("\n🎉 All basic tests passed!")
    
except Exception as e:
    print(f"❌ Test failed: {e}")
    import traceback
    traceback.print_exc()
