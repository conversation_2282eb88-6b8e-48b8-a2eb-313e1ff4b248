"""Constellation types and configurations for adaptive learning."""

from enum import Enum
from typing import List, Dict, Any
from pydantic import BaseModel, Field


class ConstellationType(str, Enum):
    """Available constellation types for different learning scenarios."""
    
    KNOWLEDGE_INTENSIVE = "knowledge_intensive"
    HANDS_ON_FOCUSED = "hands_on_focused"
    THEORY_PRACTICE_BALANCED = "theory_practice_balanced"
    BASIC_LEARNING = "basic_learning"
    GUIDED_LEARNING = "guided_learning"
    
    # Future constellation types (planned)
    RESEARCH_INTENSIVE = "research_intensive"
    QUICK_LEARNING = "quick_learning"
    DEEP_EXPLORATION = "deep_exploration"
    PROJECT_ORIENTED = "project_oriented"
    ASSESSMENT_FOCUSED = "assessment_focused"


class ConstellationConfig(BaseModel):
    """Configuration for a specific constellation type."""
    
    constellation_type: ConstellationType
    name: str = Field(..., description="Human-readable constellation name")
    description: str = Field(..., description="Constellation description")
    focus: str = Field(..., description="Primary learning focus")
    
    # Agent configuration
    primary_agents: List[str] = Field(..., description="Primary agents for this constellation")
    support_agents: List[str] = Field(..., description="Supporting agents")
    max_active_agents: int = Field(6, ge=2, le=12, description="Maximum concurrent active agents")
    
    # Learning characteristics
    theoretical_weight: float = Field(..., ge=0.0, le=1.0, description="Weight of theoretical content")
    practical_weight: float = Field(..., ge=0.0, le=1.0, description="Weight of practical content")
    assessment_frequency: str = Field(..., description="Assessment frequency (low/medium/high)")
    
    # Handoff patterns
    handoff_triggers: Dict[str, List[str]] = Field(
        default_factory=dict, description="Agent handoff trigger patterns"
    )
    
    # Best suited for
    best_for_skill_levels: List[str] = Field(..., description="Optimal skill levels")
    best_for_learning_styles: List[str] = Field(..., description="Optimal learning styles")
    best_for_paces: List[str] = Field(..., description="Optimal learning paces")


# Constellation configurations
CONSTELLATION_CONFIGS = {
    ConstellationType.KNOWLEDGE_INTENSIVE: ConstellationConfig(
        constellation_type=ConstellationType.KNOWLEDGE_INTENSIVE,
        name="Knowledge Intensive",
        description="Deep theoretical understanding with comprehensive concept exploration",
        focus="Theoretical understanding and conceptual mastery",
        primary_agents=["instructor", "documentation_expert", "knowledge_synthesizer"],
        support_agents=["research_assistant", "progress_tracker"],
        max_active_agents=5,
        theoretical_weight=0.8,
        practical_weight=0.2,
        assessment_frequency="medium",
        handoff_triggers={
            "instructor": ["documentation_expert", "knowledge_synthesizer"],
            "documentation_expert": ["research_assistant", "instructor"],
            "knowledge_synthesizer": ["progress_tracker", "instructor"],
            "research_assistant": ["documentation_expert", "knowledge_synthesizer"]
        },
        best_for_skill_levels=["intermediate", "advanced", "expert"],
        best_for_learning_styles=["theoretical", "visual"],
        best_for_paces=["slow", "moderate"]
    ),
    
    ConstellationType.HANDS_ON_FOCUSED: ConstellationConfig(
        constellation_type=ConstellationType.HANDS_ON_FOCUSED,
        name="Hands-On Focused",
        description="Practical implementation with extensive coding and project work",
        focus="Practical implementation and hands-on experience",
        primary_agents=["code_assistant", "practice_facilitator", "project_guide"],
        support_agents=["troubleshooter", "mentor"],
        max_active_agents=5,
        theoretical_weight=0.2,
        practical_weight=0.8,
        assessment_frequency="high",
        handoff_triggers={
            "code_assistant": ["practice_facilitator", "troubleshooter"],
            "practice_facilitator": ["project_guide", "code_assistant"],
            "project_guide": ["code_assistant", "mentor"],
            "troubleshooter": ["code_assistant", "mentor"]
        },
        best_for_skill_levels=["beginner", "intermediate", "advanced"],
        best_for_learning_styles=["hands_on", "mixed"],
        best_for_paces=["fast", "intensive"]
    ),
    
    ConstellationType.THEORY_PRACTICE_BALANCED: ConstellationConfig(
        constellation_type=ConstellationType.THEORY_PRACTICE_BALANCED,
        name="Theory-Practice Balanced",
        description="Balanced approach combining theoretical understanding with practical application",
        focus="Balanced theoretical and practical learning",
        primary_agents=["instructor", "code_assistant", "practice_facilitator"],
        support_agents=["documentation_expert", "mentor"],
        max_active_agents=5,
        theoretical_weight=0.5,
        practical_weight=0.5,
        assessment_frequency="medium",
        handoff_triggers={
            "instructor": ["code_assistant", "documentation_expert"],
            "code_assistant": ["practice_facilitator", "instructor"],
            "practice_facilitator": ["mentor", "code_assistant"],
            "documentation_expert": ["instructor", "code_assistant"]
        },
        best_for_skill_levels=["beginner", "intermediate", "advanced"],
        best_for_learning_styles=["mixed", "hands_on", "theoretical"],
        best_for_paces=["moderate", "fast"]
    ),
    
    ConstellationType.BASIC_LEARNING: ConstellationConfig(
        constellation_type=ConstellationType.BASIC_LEARNING,
        name="Basic Learning",
        description="Gentle introduction with foundational concepts and simple exercises",
        focus="Foundational learning with gentle progression",
        primary_agents=["instructor", "code_assistant"],
        support_agents=["mentor", "practice_facilitator"],
        max_active_agents=4,
        theoretical_weight=0.6,
        practical_weight=0.4,
        assessment_frequency="low",
        handoff_triggers={
            "instructor": ["code_assistant", "mentor"],
            "code_assistant": ["practice_facilitator", "mentor"],
            "mentor": ["instructor", "code_assistant"]
        },
        best_for_skill_levels=["none", "beginner"],
        best_for_learning_styles=["visual", "mixed"],
        best_for_paces=["slow", "moderate"]
    ),
    
    ConstellationType.GUIDED_LEARNING: ConstellationConfig(
        constellation_type=ConstellationType.GUIDED_LEARNING,
        name="Guided Learning",
        description="Structured guidance with step-by-step progression and mentorship",
        focus="Structured guidance and mentorship",
        primary_agents=["instructor", "mentor"],
        support_agents=["code_assistant", "practice_facilitator"],
        max_active_agents=4,
        theoretical_weight=0.7,
        practical_weight=0.3,
        assessment_frequency="medium",
        handoff_triggers={
            "instructor": ["mentor", "code_assistant"],
            "mentor": ["instructor", "practice_facilitator"],
            "code_assistant": ["mentor", "practice_facilitator"]
        },
        best_for_skill_levels=["none", "beginner", "intermediate"],
        best_for_learning_styles=["visual", "theoretical"],
        best_for_paces=["slow", "moderate"]
    )
}


def get_constellation_config(constellation_type: ConstellationType) -> ConstellationConfig:
    """Get configuration for a specific constellation type."""
    return CONSTELLATION_CONFIGS[constellation_type]


def get_available_constellations() -> List[ConstellationType]:
    """Get list of available constellation types."""
    return [
        ConstellationType.KNOWLEDGE_INTENSIVE,
        ConstellationType.HANDS_ON_FOCUSED,
        ConstellationType.THEORY_PRACTICE_BALANCED,
        ConstellationType.BASIC_LEARNING,
        ConstellationType.GUIDED_LEARNING
    ]


def recommend_constellation_for_profile(
    skill_level: str,
    learning_style: str,
    learning_pace: str
) -> List[ConstellationType]:
    """Recommend constellation types based on user profile."""
    recommendations = []
    
    for constellation_type in get_available_constellations():
        config = get_constellation_config(constellation_type)
        
        skill_match = skill_level in config.best_for_skill_levels
        style_match = learning_style in config.best_for_learning_styles
        pace_match = learning_pace in config.best_for_paces
        
        # Score based on matches
        score = sum([skill_match, style_match, pace_match])
        
        if score >= 2:  # At least 2 out of 3 criteria match
            recommendations.append(constellation_type)
    
    return recommendations
