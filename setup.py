"""Setup script for GAAPF - Guidance AI Agent for Python Framework."""

from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="gaapf-guidance-ai-agent",
    version="1.0.0",
    author="GAAPF Development Team",
    author_email="<EMAIL>",
    description="Guidance AI Agent for Python Framework - Adaptive Multi-Agent Learning System",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/your-username/gaapf-guidance-ai-agent",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Intended Audience :: Education",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Topic :: Education",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    python_requires=">=3.10",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.4.0",
            "pytest-asyncio>=0.21.0",
            "black>=23.9.0",
            "ruff>=0.1.0",
            "mypy>=1.6.0",
        ],
        "viz": [
            "matplotlib>=3.7.0",
            "plotly>=5.17.0",
            "seaborn>=0.12.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "gaapf-cli=pyframeworks_assistant.interfaces.cli.main:main",
        ],
    },
    include_package_data=True,
    zip_safe=False,
)
