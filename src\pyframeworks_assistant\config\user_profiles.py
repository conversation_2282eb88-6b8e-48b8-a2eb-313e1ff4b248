"""User profile configuration and management for GAAPF system."""

from enum import Enum
from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, validator
import json
from pathlib import Path


class SkillLevel(str, Enum):
    """User skill level enumeration."""
    NONE = "none"
    BEGINNER = "beginner"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"
    EXPERT = "expert"


class LearningPace(str, Enum):
    """Learning pace preference enumeration."""
    SLOW = "slow"
    MODERATE = "moderate"
    FAST = "fast"
    INTENSIVE = "intensive"


class LearningStyle(str, Enum):
    """Learning style preference enumeration."""
    VISUAL = "visual"
    HANDS_ON = "hands_on"
    THEORETICAL = "theoretical"
    MIXED = "mixed"


class DifficultyProgression(str, Enum):
    """Difficulty progression preference enumeration."""
    GRADUAL = "gradual"
    MODERATE = "moderate"
    AGGRESSIVE = "aggressive"


class UserProfile(BaseModel):
    """Comprehensive user profile for personalized learning."""
    
    user_id: str = Field(..., description="Unique user identifier")
    name: Optional[str] = Field(None, description="User's display name")
    email: Optional[str] = Field(None, description="User's email address")
    
    # Experience and Skills
    programming_experience_years: int = Field(
        0, ge=0, le=50, description="Years of programming experience"
    )
    python_skill_level: SkillLevel = Field(
        SkillLevel.BEGINNER, description="Python proficiency level"
    )
    ai_ml_experience: SkillLevel = Field(
        SkillLevel.NONE, description="AI/ML experience level"
    )
    
    # Learning Preferences
    learning_pace: LearningPace = Field(
        LearningPace.MODERATE, description="Preferred learning pace"
    )
    preferred_learning_style: LearningStyle = Field(
        LearningStyle.MIXED, description="Preferred learning style"
    )
    difficulty_progression: DifficultyProgression = Field(
        DifficultyProgression.MODERATE, description="Difficulty progression preference"
    )
    
    # Goals and Interests
    learning_goals: List[str] = Field(
        default_factory=list, description="User's learning objectives"
    )
    frameworks_of_interest: List[str] = Field(
        default_factory=list, description="AI frameworks user wants to learn"
    )
    project_types: List[str] = Field(
        default_factory=list, description="Types of projects user wants to build"
    )
    
    # Session Preferences
    preferred_session_length_minutes: int = Field(
        30, ge=5, le=180, description="Preferred learning session length"
    )
    max_daily_learning_time_minutes: int = Field(
        120, ge=15, le=480, description="Maximum daily learning time"
    )
    
    # Tracking and Analytics
    created_at: datetime = Field(default_factory=datetime.now)
    last_updated: datetime = Field(default_factory=datetime.now)
    total_learning_time_minutes: int = Field(0, ge=0)
    completed_modules: List[str] = Field(default_factory=list)
    current_streak_days: int = Field(0, ge=0)
    
    # Adaptive Learning Data
    constellation_preferences: Dict[str, float] = Field(
        default_factory=dict, description="Learned constellation effectiveness scores"
    )
    learning_patterns: Dict[str, Any] = Field(
        default_factory=dict, description="Temporal learning patterns"
    )
    
    @validator('email')
    def validate_email(cls, v):
        """Basic email validation."""
        if v and '@' not in v:
            raise ValueError('Invalid email format')
        return v
    
    @validator('learning_goals', 'frameworks_of_interest', 'project_types')
    def validate_lists(cls, v):
        """Ensure lists don't contain empty strings."""
        return [item.strip() for item in v if item.strip()]
    
    def update_learning_time(self, minutes: int) -> None:
        """Update total learning time and last updated timestamp."""
        self.total_learning_time_minutes += minutes
        self.last_updated = datetime.now()
    
    def add_completed_module(self, module_id: str) -> None:
        """Add a completed module to the user's record."""
        if module_id not in self.completed_modules:
            self.completed_modules.append(module_id)
            self.last_updated = datetime.now()
    
    def update_constellation_preference(self, constellation_type: str, effectiveness: float) -> None:
        """Update constellation effectiveness score."""
        self.constellation_preferences[constellation_type] = effectiveness
        self.last_updated = datetime.now()
    
    def get_experience_level(self) -> str:
        """Get overall experience level based on multiple factors."""
        if self.programming_experience_years >= 5 and self.python_skill_level in [SkillLevel.ADVANCED, SkillLevel.EXPERT]:
            return "experienced"
        elif self.programming_experience_years >= 2 and self.python_skill_level in [SkillLevel.INTERMEDIATE, SkillLevel.ADVANCED]:
            return "intermediate"
        else:
            return "beginner"
    
    def save_to_file(self, file_path: Path) -> None:
        """Save user profile to JSON file."""
        file_path.parent.mkdir(parents=True, exist_ok=True)
        with open(file_path, 'w') as f:
            json.dump(self.dict(), f, indent=2, default=str)
    
    @classmethod
    def load_from_file(cls, file_path: Path) -> 'UserProfile':
        """Load user profile from JSON file."""
        with open(file_path, 'r') as f:
            data = json.load(f)
        
        # Convert datetime strings back to datetime objects
        if 'created_at' in data and isinstance(data['created_at'], str):
            data['created_at'] = datetime.fromisoformat(data['created_at'])
        if 'last_updated' in data and isinstance(data['last_updated'], str):
            data['last_updated'] = datetime.fromisoformat(data['last_updated'])
        
        return cls(**data)
    
    model_config = {
        "use_enum_values": True,
        "json_encoders": {
            datetime: lambda v: v.isoformat()
        }
    }
