# Core LangChain and LangGraph dependencies
langchain>=0.3.25
langchain-core>=0.3.25
langchain-community>=0.3.25
langgraph>=0.4.7
langsmith>=0.1.0

# LLM Provider integrations
langchain-google-genai>=2.0.0
langchain-openai>=0.2.0
langchain-anthropic>=0.2.0

# Data validation and configuration
pydantic>=2.5.0
pydantic-settings>=2.1.0

# Search and web tools
tavily-python>=0.5.0
requests>=2.31.0
beautifulsoup4>=4.12.0

# File and code tools
python-dotenv>=1.0.0
pathlib>=1.0.0

# CLI and interface
click>=8.1.0
rich>=13.7.0
streamlit>=1.28.0

# Data handling and storage
pandas>=2.1.0
numpy>=1.24.0
json5>=0.9.0

# Async support
asyncio>=3.4.3
aiofiles>=23.2.0

# Testing and development
pytest>=7.4.0
pytest-asyncio>=0.21.0
black>=23.9.0
ruff>=0.1.0
mypy>=1.6.0

# Optional dependencies for enhanced features
matplotlib>=3.7.0
plotly>=5.17.0
seaborn>=0.12.0
