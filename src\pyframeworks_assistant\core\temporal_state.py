"""Temporal state manager for learning pattern optimization."""

import json
import asyncio
from typing import Dict, List, Optional, Any, Tu<PERSON>
from datetime import datetime, timedelta
from pathlib import Path
from pydantic import BaseModel, Field
import numpy as np
from collections import defaultdict

from ..config.user_profiles import UserProfile
from ..config.framework_configs import SupportedFrameworks
from .constellation_types import ConstellationType, get_available_constellations


class EffectivenessMetrics(BaseModel):
    """Metrics for measuring learning effectiveness."""
    
    comprehension_score: float = Field(0.0, ge=0.0, le=1.0, description="Understanding level")
    engagement_score: float = Field(0.0, ge=0.0, le=1.0, description="User engagement level")
    completion_rate: float = Field(0.0, ge=0.0, le=1.0, description="Task completion rate")
    satisfaction_score: float = Field(0.0, ge=0.0, le=1.0, description="User satisfaction")
    efficiency_score: float = Field(0.0, ge=0.0, le=1.0, description="Learning efficiency")
    retention_estimate: float = Field(0.0, ge=0.0, le=1.0, description="Knowledge retention prediction")
    
    def calculate_overall_effectiveness(self) -> float:
        """Calculate overall effectiveness score with weighted components."""
        weights = {
            'comprehension': 0.25,
            'engagement': 0.20,
            'completion': 0.20,
            'satisfaction': 0.15,
            'efficiency': 0.10,
            'retention': 0.10
        }
        
        return (
            self.comprehension_score * weights['comprehension'] +
            self.engagement_score * weights['engagement'] +
            self.completion_rate * weights['completion'] +
            self.satisfaction_score * weights['satisfaction'] +
            self.efficiency_score * weights['efficiency'] +
            self.retention_estimate * weights['retention']
        )


class LearningSession(BaseModel):
    """Record of a learning session for temporal analysis."""
    
    session_id: str
    user_id: str
    timestamp: datetime
    constellation_type: ConstellationType
    framework: SupportedFrameworks
    module_id: str
    
    # Session metrics
    duration_minutes: float
    interactions_count: int
    agents_used: List[str]
    handoff_count: int
    
    # Effectiveness metrics
    effectiveness_metrics: EffectivenessMetrics
    overall_effectiveness: float
    
    # Context
    user_skill_level: str
    learning_style: str
    learning_pace: str
    session_context: Dict[str, Any] = Field(default_factory=dict)


class LearningPattern(BaseModel):
    """Identified learning pattern for optimization."""
    
    pattern_id: str
    user_id: str
    constellation_type: ConstellationType
    framework: SupportedFrameworks
    
    # Pattern characteristics
    optimal_conditions: Dict[str, Any]
    effectiveness_trend: List[float]
    confidence_score: float
    
    # Temporal aspects
    time_of_day_preference: Optional[str] = None
    session_length_preference: Optional[int] = None
    learning_velocity: float = 0.0
    
    # Pattern metadata
    sessions_analyzed: int
    last_updated: datetime
    pattern_strength: float


class TemporalStateManager:
    """Manager for temporal learning optimization and pattern recognition."""
    
    def __init__(self, data_dir: str = "temporal_data"):
        """Initialize the temporal state manager.
        
        Args:
            data_dir: Directory for storing temporal data
        """
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True)
        
        # In-memory caches
        self.session_cache: Dict[str, LearningSession] = {}
        self.pattern_cache: Dict[str, List[LearningPattern]] = {}
        self.effectiveness_cache: Dict[str, Dict[ConstellationType, float]] = {}
        
        # Load existing data
        asyncio.create_task(self._load_historical_data())
    
    async def _load_historical_data(self) -> None:
        """Load historical learning data from storage."""
        try:
            # Load sessions
            sessions_file = self.data_dir / "learning_sessions.json"
            if sessions_file.exists():
                with open(sessions_file, 'r') as f:
                    sessions_data = json.load(f)
                    for session_data in sessions_data:
                        session = LearningSession(**session_data)
                        self.session_cache[session.session_id] = session
            
            # Load patterns
            patterns_file = self.data_dir / "learning_patterns.json"
            if patterns_file.exists():
                with open(patterns_file, 'r') as f:
                    patterns_data = json.load(f)
                    for user_id, user_patterns in patterns_data.items():
                        self.pattern_cache[user_id] = [
                            LearningPattern(**pattern) for pattern in user_patterns
                        ]
            
            # Rebuild effectiveness cache
            await self._rebuild_effectiveness_cache()
            
        except Exception as e:
            print(f"Warning: Could not load historical data: {e}")
    
    async def record_session(
        self,
        session_id: str,
        user_profile: UserProfile,
        constellation_type: ConstellationType,
        framework: SupportedFrameworks,
        module_id: str,
        session_data: Dict[str, Any]
    ) -> None:
        """Record a completed learning session.
        
        Args:
            session_id: Unique session identifier
            user_profile: User profile
            constellation_type: Type of constellation used
            framework: Framework being learned
            module_id: Module within framework
            session_data: Session metrics and data
        """
        # Calculate effectiveness metrics
        effectiveness = await self._calculate_effectiveness_metrics(session_data, user_profile)
        
        # Create session record
        session = LearningSession(
            session_id=session_id,
            user_id=user_profile.user_id,
            timestamp=datetime.now(),
            constellation_type=constellation_type,
            framework=framework,
            module_id=module_id,
            duration_minutes=session_data.get('duration_minutes', 0),
            interactions_count=session_data.get('interactions_count', 0),
            agents_used=session_data.get('agents_used', []),
            handoff_count=session_data.get('handoff_count', 0),
            effectiveness_metrics=effectiveness,
            overall_effectiveness=effectiveness.calculate_overall_effectiveness(),
            user_skill_level=user_profile.python_skill_level.value,
            learning_style=user_profile.preferred_learning_style.value,
            learning_pace=user_profile.learning_pace.value,
            session_context=session_data.get('context', {})
        )
        
        # Store session
        self.session_cache[session_id] = session
        
        # Update patterns
        await self._update_learning_patterns(user_profile.user_id, session)
        
        # Update effectiveness cache
        await self._update_effectiveness_cache(user_profile.user_id, constellation_type, session.overall_effectiveness)
        
        # Persist data
        await self._persist_data()
    
    async def _calculate_effectiveness_metrics(
        self,
        session_data: Dict[str, Any],
        user_profile: UserProfile
    ) -> EffectivenessMetrics:
        """Calculate effectiveness metrics for a session."""
        
        # Extract metrics from session data
        duration = session_data.get('duration_minutes', 30)
        interactions = session_data.get('interactions_count', 1)
        completion_rate = session_data.get('completion_rate', 0.5)
        user_feedback = session_data.get('user_feedback', {})
        
        # Calculate comprehension score (simplified - would use LLM analysis in practice)
        comprehension_score = min(1.0, completion_rate + 0.2)
        
        # Calculate engagement score based on interaction frequency
        expected_interactions = duration / 5  # Expect interaction every 5 minutes
        engagement_score = min(1.0, interactions / max(1, expected_interactions))
        
        # Calculate efficiency score (learning per unit time)
        efficiency_score = min(1.0, completion_rate / max(0.1, duration / 30))
        
        # Use user feedback if available
        satisfaction_score = user_feedback.get('satisfaction', 0.7)
        
        # Estimate retention based on comprehension and engagement
        retention_estimate = (comprehension_score * 0.6 + engagement_score * 0.4) * 0.8
        
        return EffectivenessMetrics(
            comprehension_score=comprehension_score,
            engagement_score=engagement_score,
            completion_rate=completion_rate,
            satisfaction_score=satisfaction_score,
            efficiency_score=efficiency_score,
            retention_estimate=retention_estimate
        )
    
    async def optimize_constellation_selection(
        self,
        user_profile: UserProfile,
        framework: SupportedFrameworks,
        module_id: str,
        session_context: Dict[str, Any]
    ) -> Tuple[ConstellationType, float]:
        """Optimize constellation selection based on historical patterns.
        
        Args:
            user_profile: User profile for personalization
            framework: Framework being learned
            module_id: Module within framework
            session_context: Current session context
            
        Returns:
            Tuple of (optimal_constellation_type, confidence_score)
        """
        user_id = user_profile.user_id
        
        # Get user's historical effectiveness for each constellation type
        user_effectiveness = self.effectiveness_cache.get(user_id, {})
        
        if not user_effectiveness:
            # No historical data - use profile-based recommendation
            from .constellation_types import recommend_constellation_for_profile
            recommendations = recommend_constellation_for_profile(
                user_profile.python_skill_level.value,
                user_profile.preferred_learning_style.value,
                user_profile.learning_pace.value
            )
            return recommendations[0] if recommendations else ConstellationType.THEORY_PRACTICE_BALANCED, 0.5
        
        # Find the most effective constellation type for this user
        best_constellation = max(user_effectiveness.items(), key=lambda x: x[1])
        constellation_type, effectiveness = best_constellation
        
        # Calculate confidence based on number of sessions and effectiveness variance
        user_sessions = [s for s in self.session_cache.values() if s.user_id == user_id]
        constellation_sessions = [s for s in user_sessions if s.constellation_type == constellation_type]
        
        if len(constellation_sessions) < 3:
            confidence = 0.6  # Low confidence with few sessions
        else:
            # Higher confidence with more sessions and consistent effectiveness
            effectiveness_variance = np.var([s.overall_effectiveness for s in constellation_sessions])
            confidence = min(0.95, 0.7 + (len(constellation_sessions) * 0.05) - effectiveness_variance)
        
        return constellation_type, confidence
    
    async def _update_learning_patterns(self, user_id: str, session: LearningSession) -> None:
        """Update learning patterns based on new session data."""
        
        if user_id not in self.pattern_cache:
            self.pattern_cache[user_id] = []
        
        # Find existing pattern for this constellation type and framework
        existing_pattern = None
        for pattern in self.pattern_cache[user_id]:
            if (pattern.constellation_type == session.constellation_type and 
                pattern.framework == session.framework):
                existing_pattern = pattern
                break
        
        if existing_pattern:
            # Update existing pattern
            existing_pattern.effectiveness_trend.append(session.overall_effectiveness)
            existing_pattern.sessions_analyzed += 1
            existing_pattern.last_updated = datetime.now()
            
            # Update pattern strength based on trend consistency
            if len(existing_pattern.effectiveness_trend) >= 3:
                recent_trend = existing_pattern.effectiveness_trend[-3:]
                trend_variance = np.var(recent_trend)
                existing_pattern.pattern_strength = max(0.0, 1.0 - trend_variance)
        else:
            # Create new pattern
            new_pattern = LearningPattern(
                pattern_id=f"{user_id}_{session.constellation_type.value}_{session.framework.value}",
                user_id=user_id,
                constellation_type=session.constellation_type,
                framework=session.framework,
                optimal_conditions={
                    "skill_level": session.user_skill_level,
                    "learning_style": session.learning_style,
                    "learning_pace": session.learning_pace
                },
                effectiveness_trend=[session.overall_effectiveness],
                confidence_score=0.5,
                sessions_analyzed=1,
                last_updated=datetime.now(),
                pattern_strength=0.5
            )
            self.pattern_cache[user_id].append(new_pattern)
    
    async def _update_effectiveness_cache(
        self,
        user_id: str,
        constellation_type: ConstellationType,
        effectiveness: float
    ) -> None:
        """Update the effectiveness cache with new data."""
        
        if user_id not in self.effectiveness_cache:
            self.effectiveness_cache[user_id] = {}
        
        if constellation_type not in self.effectiveness_cache[user_id]:
            self.effectiveness_cache[user_id][constellation_type] = effectiveness
        else:
            # Use exponential moving average to update effectiveness
            current = self.effectiveness_cache[user_id][constellation_type]
            alpha = 0.3  # Learning rate
            self.effectiveness_cache[user_id][constellation_type] = (
                alpha * effectiveness + (1 - alpha) * current
            )
    
    async def _rebuild_effectiveness_cache(self) -> None:
        """Rebuild effectiveness cache from session data."""
        self.effectiveness_cache.clear()
        
        for session in self.session_cache.values():
            await self._update_effectiveness_cache(
                session.user_id,
                session.constellation_type,
                session.overall_effectiveness
            )
    
    async def _persist_data(self) -> None:
        """Persist temporal data to storage."""
        try:
            # Save sessions
            sessions_data = [session.dict() for session in self.session_cache.values()]
            with open(self.data_dir / "learning_sessions.json", 'w') as f:
                json.dump(sessions_data, f, indent=2, default=str)
            
            # Save patterns
            patterns_data = {}
            for user_id, patterns in self.pattern_cache.items():
                patterns_data[user_id] = [pattern.dict() for pattern in patterns]
            
            with open(self.data_dir / "learning_patterns.json", 'w') as f:
                json.dump(patterns_data, f, indent=2, default=str)
                
        except Exception as e:
            print(f"Warning: Could not persist temporal data: {e}")
    
    def get_user_learning_analytics(self, user_id: str) -> Dict[str, Any]:
        """Get learning analytics for a specific user."""
        
        user_sessions = [s for s in self.session_cache.values() if s.user_id == user_id]
        user_patterns = self.pattern_cache.get(user_id, [])
        user_effectiveness = self.effectiveness_cache.get(user_id, {})
        
        if not user_sessions:
            return {"error": "No learning data found for user"}
        
        # Calculate analytics
        total_sessions = len(user_sessions)
        total_learning_time = sum(s.duration_minutes for s in user_sessions)
        avg_effectiveness = np.mean([s.overall_effectiveness for s in user_sessions])
        
        # Most effective constellation
        best_constellation = None
        if user_effectiveness:
            best_constellation = max(user_effectiveness.items(), key=lambda x: x[1])
        
        return {
            "user_id": user_id,
            "total_sessions": total_sessions,
            "total_learning_time_minutes": total_learning_time,
            "average_effectiveness": avg_effectiveness,
            "best_constellation": {
                "type": best_constellation[0].value if best_constellation else None,
                "effectiveness": best_constellation[1] if best_constellation else None
            },
            "learning_patterns_identified": len(user_patterns),
            "recent_trend": [s.overall_effectiveness for s in user_sessions[-5:]] if user_sessions else []
        }
