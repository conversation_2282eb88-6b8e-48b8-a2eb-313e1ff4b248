"""Memory manager for coordinating different memory systems."""

import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from pathlib import Path

from ..config.user_profiles import UserProfile
from .conversation_memory import ConversationMemory
from .knowledge_memory import KnowledgeMemory
from .user_memory import UserMemory


class MemoryManager:
    """Coordinates different memory systems for GAAPF."""
    
    def __init__(self, data_dir: str = "memory_data"):
        """Initialize the memory manager.
        
        Args:
            data_dir: Directory for storing memory data
        """
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True)
        
        # Memory systems
        self.conversation_memory = ConversationMemory(self.data_dir / "conversations")
        self.knowledge_memory = KnowledgeMemory(self.data_dir / "knowledge")
        self.user_memory = UserMemory(self.data_dir / "users")
        
        # Active session memories
        self.active_sessions: Dict[str, Dict[str, Any]] = {}
        
        self.is_initialized = False
    
    async def initialize(self) -> None:
        """Initialize all memory systems."""
        if self.is_initialized:
            return
        
        await asyncio.gather(
            self.conversation_memory.initialize(),
            self.knowledge_memory.initialize(),
            self.user_memory.initialize()
        )
        
        self.is_initialized = True
    
    async def create_session_memory(self, session_id: str, user_profile: UserProfile) -> None:
        """Create memory context for a new learning session.
        
        Args:
            session_id: Session identifier
            user_profile: User profile for context
        """
        if not self.is_initialized:
            await self.initialize()
        
        # Initialize session memory
        self.active_sessions[session_id] = {
            "user_id": user_profile.user_id,
            "start_time": datetime.now(),
            "interactions": [],
            "context": {}
        }
        
        # Load relevant user memory
        user_context = await self.user_memory.get_user_context(user_profile.user_id)
        self.active_sessions[session_id]["context"].update(user_context)
        
        # Initialize conversation memory for session
        await self.conversation_memory.start_session(session_id, user_profile.user_id)
    
    async def add_interaction(
        self,
        session_id: str,
        user_message: Optional[str] = None,
        agent_response: Optional[str] = None,
        agent_role: Optional[str] = None,
        timestamp: Optional[datetime] = None
    ) -> None:
        """Add an interaction to session memory.
        
        Args:
            session_id: Session identifier
            user_message: User's message (if any)
            agent_response: Agent's response (if any)
            agent_role: Role of responding agent
            timestamp: Interaction timestamp
        """
        if session_id not in self.active_sessions:
            return
        
        timestamp = timestamp or datetime.now()
        
        interaction = {
            "timestamp": timestamp,
            "user_message": user_message,
            "agent_response": agent_response,
            "agent_role": agent_role
        }
        
        # Add to session memory
        self.active_sessions[session_id]["interactions"].append(interaction)
        
        # Store in conversation memory
        if user_message:
            await self.conversation_memory.add_user_message(session_id, user_message, timestamp)
        
        if agent_response:
            await self.conversation_memory.add_agent_response(
                session_id, agent_response, agent_role, timestamp
            )
        
        # Update knowledge memory if concepts were discussed
        if user_message or agent_response:
            concepts = self._extract_concepts(user_message or "", agent_response or "")
            if concepts:
                user_id = self.active_sessions[session_id]["user_id"]
                await self.knowledge_memory.update_concept_exposure(user_id, concepts, timestamp)
    
    def _extract_concepts(self, user_message: str, agent_response: str) -> List[str]:
        """Extract concepts from messages (simplified implementation)."""
        # This would use more sophisticated NLP in practice
        concepts = []
        text = (user_message + " " + agent_response).lower()
        
        # Framework concepts
        framework_concepts = [
            "langchain", "langgraph", "chain", "agent", "llm", "prompt", 
            "memory", "vector", "embedding", "rag", "tool", "function"
        ]
        
        for concept in framework_concepts:
            if concept in text:
                concepts.append(concept)
        
        return list(set(concepts))
    
    async def get_session_context(self, session_id: str) -> Dict[str, Any]:
        """Get context for a session including relevant memories.
        
        Args:
            session_id: Session identifier
            
        Returns:
            Session context with relevant memories
        """
        if session_id not in self.active_sessions:
            return {}
        
        session = self.active_sessions[session_id]
        user_id = session["user_id"]
        
        # Get conversation history
        conversation_history = await self.conversation_memory.get_session_history(session_id)
        
        # Get relevant knowledge
        recent_concepts = []
        for interaction in session["interactions"][-5:]:  # Last 5 interactions
            concepts = self._extract_concepts(
                interaction.get("user_message", ""),
                interaction.get("agent_response", "")
            )
            recent_concepts.extend(concepts)
        
        knowledge_context = await self.knowledge_memory.get_concept_context(
            user_id, list(set(recent_concepts))
        )
        
        # Get user context
        user_context = await self.user_memory.get_user_context(user_id)
        
        return {
            "session_id": session_id,
            "conversation_history": conversation_history,
            "knowledge_context": knowledge_context,
            "user_context": user_context,
            "session_stats": {
                "interactions_count": len(session["interactions"]),
                "duration_minutes": (datetime.now() - session["start_time"]).total_seconds() / 60
            }
        }
    
    async def update_user_learning_state(
        self,
        user_id: str,
        concepts_learned: List[str],
        skills_practiced: List[str],
        session_summary: Dict[str, Any]
    ) -> None:
        """Update user's learning state based on session.
        
        Args:
            user_id: User identifier
            concepts_learned: Concepts learned in session
            skills_practiced: Skills practiced in session
            session_summary: Summary of session outcomes
        """
        # Update knowledge memory
        await self.knowledge_memory.update_concept_mastery(
            user_id, concepts_learned, session_summary.get("mastery_scores", {})
        )
        
        # Update user memory
        await self.user_memory.update_learning_progress(
            user_id, concepts_learned, skills_practiced, session_summary
        )
    
    async def archive_session_memory(self, session_id: str) -> None:
        """Archive session memory and clean up active session.
        
        Args:
            session_id: Session to archive
        """
        if session_id not in self.active_sessions:
            return
        
        session = self.active_sessions[session_id]
        user_id = session["user_id"]
        
        # Archive conversation
        await self.conversation_memory.archive_session(session_id)
        
        # Update user memory with session summary
        session_summary = {
            "session_id": session_id,
            "duration_minutes": (datetime.now() - session["start_time"]).total_seconds() / 60,
            "interactions_count": len(session["interactions"]),
            "end_time": datetime.now()
        }
        
        await self.user_memory.add_session_record(user_id, session_summary)
        
        # Clean up active session
        del self.active_sessions[session_id]
    
    async def get_user_memory_summary(self, user_id: str) -> Dict[str, Any]:
        """Get comprehensive memory summary for a user.
        
        Args:
            user_id: User identifier
            
        Returns:
            Memory summary across all systems
        """
        # Get summaries from each memory system
        conversation_summary = await self.conversation_memory.get_user_summary(user_id)
        knowledge_summary = await self.knowledge_memory.get_user_summary(user_id)
        user_summary = await self.user_memory.get_user_summary(user_id)
        
        return {
            "user_id": user_id,
            "conversation_memory": conversation_summary,
            "knowledge_memory": knowledge_summary,
            "user_memory": user_summary,
            "active_sessions": [
                session_id for session_id, session in self.active_sessions.items()
                if session["user_id"] == user_id
            ]
        }
    
    async def cleanup_old_memories(self, days_threshold: int = 30) -> None:
        """Clean up old memory data.
        
        Args:
            days_threshold: Number of days after which to clean up data
        """
        cutoff_date = datetime.now() - timedelta(days=days_threshold)
        
        await asyncio.gather(
            self.conversation_memory.cleanup_old_data(cutoff_date),
            self.knowledge_memory.cleanup_old_data(cutoff_date),
            self.user_memory.cleanup_old_data(cutoff_date)
        )
    
    def get_active_sessions(self) -> List[str]:
        """Get list of active session IDs."""
        return list(self.active_sessions.keys())
    
    def get_session_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get information about an active session."""
        if session_id not in self.active_sessions:
            return None
        
        session = self.active_sessions[session_id]
        return {
            "session_id": session_id,
            "user_id": session["user_id"],
            "start_time": session["start_time"],
            "interactions_count": len(session["interactions"]),
            "duration_minutes": (datetime.now() - session["start_time"]).total_seconds() / 60
        }
