#!/usr/bin/env python3
"""Test ToolManager specifically."""

import sys
from pathlib import Path

# Add src to path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

print("🧪 Testing ToolManager...")

try:
    print("1. Testing ToolManager import...")
    from pyframeworks_assistant.tools.tool_manager import ToolManager
    print("✅ ToolManager imported successfully")
    
    print("2. Creating ToolManager instance...")
    tm = ToolManager()
    print(f"✅ ToolManager created")
    print(f"   Available tools: {len(tm.available_tools)}")
    print(f"   Tool names: {list(tm.available_tools.keys())}")
    
    print("3. Testing tool info...")
    info = tm.get_tool_info()
    print(f"✅ Tool info retrieved")
    print(f"   Total tools: {info['total_tools']}")
    print(f"   Available: {len(info['available_tools'])}")
    print(f"   Unavailable: {len(info['unavailable_tools'])}")
    
    if info['unavailable_tools']:
        print(f"   Unavailable tools: {info['unavailable_tools']}")
    
    print("\n🎉 ToolManager test passed!")
    
except Exception as e:
    print(f"❌ ToolManager test failed: {e}")
    import traceback
    traceback.print_exc()
