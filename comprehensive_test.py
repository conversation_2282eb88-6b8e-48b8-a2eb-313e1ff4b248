#!/usr/bin/env python3
"""Comprehensive test to verify all fixes are working."""

import sys
from pathlib import Path

# Add src to path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def test_pydantic_settings():
    """Test Pydantic 2.x and pydantic-settings compatibility."""
    print("🧪 Testing Pydantic 2.x compatibility...")
    try:
        import pydantic
        from pydantic_settings import BaseSettings
        from pydantic import BaseModel, Field
        
        print(f"✅ Pydantic version: {pydantic.VERSION}")
        print("✅ pydantic-settings imported successfully")
        
        # Test BaseSettings with Pydantic 2.x syntax
        class TestConfig(BaseSettings):
            test_field: str = Field("default", alias="TEST_FIELD")
            model_config = {
                "env_file": ".env",
                "case_sensitive": False
            }
        
        config = TestConfig()
        print(f"✅ BaseSettings with model_config works: {config.test_field}")
        return True
    except Exception as e:
        print(f"❌ Pydantic test failed: {e}")
        return False

def test_system_config():
    """Test SystemConfig with fixed BaseSettings import."""
    print("\n🧪 Testing SystemConfig...")
    try:
        from pyframeworks_assistant.config.system_config import SystemConfig, system_config
        print("✅ SystemConfig imported successfully")
        
        # Test instance creation
        config = SystemConfig()
        print(f"✅ SystemConfig instance: log_level={config.log_level}, debug={config.debug}")
        
        # Test global instance
        print(f"✅ Global config: max_agents={system_config.max_concurrent_agents}")
        
        # Test environment variable handling
        print(f"✅ API key handling: has_llm_key={system_config.has_llm_api_key()}")
        
        return True
    except Exception as e:
        print(f"❌ SystemConfig test failed: {e}")
        return False

def test_tavily_tools():
    """Test Tavily tools with Pydantic 2.x compatibility."""
    print("\n🧪 Testing Tavily tools...")
    try:
        from pyframeworks_assistant.tools.tavily_tools import (
            TavilySearchTool, TavilyExtractTool, TavilyCrawlTool,
            create_tavily_search_tool, get_tavily_tools
        )
        print("✅ Tavily tools imported successfully")
        
        # Test custom tools
        search_tool = TavilySearchTool(api_key="test_key")
        extract_tool = TavilyExtractTool(api_key="test_key")
        crawl_tool = TavilyCrawlTool(api_key="test_key")
        
        print(f"✅ Custom tools created: {search_tool.name}, {extract_tool.name}, {crawl_tool.name}")
        
        # Test factory function
        factory_tool = create_tavily_search_tool("test_key")
        print(f"✅ Factory tool: {factory_tool.name} ({type(factory_tool).__name__})")
        
        # Test get_tavily_tools
        all_tools = get_tavily_tools("test_key")
        print(f"✅ Factory function returned {len(all_tools)} tools")
        
        return True
    except Exception as e:
        print(f"❌ Tavily tools test failed: {e}")
        return False

def test_all_tools():
    """Test all tools through ToolManager."""
    print("\n🧪 Testing all tools via ToolManager...")
    try:
        from pyframeworks_assistant.tools.tool_manager import ToolManager
        
        tm = ToolManager()
        print(f"✅ ToolManager created with {len(tm.available_tools)} tools")
        
        # Get tool info
        info = tm.get_tool_info()
        print(f"✅ Available tools: {len(info['available_tools'])}")
        print(f"✅ Unavailable tools: {len(info['unavailable_tools'])}")
        
        if info['unavailable_tools']:
            print(f"   Unavailable: {info['unavailable_tools']}")
        
        # Test tool categories
        search_tools = tm.get_tools_by_category("search")
        file_tools = tm.get_tools_by_category("file")
        learning_tools = tm.get_tools_by_category("learning")
        
        print(f"✅ Tool categories: search={len(search_tools)}, file={len(file_tools)}, learning={len(learning_tools)}")
        
        return len(info['unavailable_tools']) == 0
    except Exception as e:
        print(f"❌ ToolManager test failed: {e}")
        return False

def test_langchain_integration():
    """Test LangChain 0.3.x integration."""
    print("\n🧪 Testing LangChain 0.3.x integration...")
    try:
        import langchain
        from langchain_core.tools import BaseTool
        from langchain_community.tools.tavily_search import TavilySearchResults
        
        print(f"✅ LangChain version: {langchain.__version__}")
        print("✅ LangChain core tools imported")
        print("✅ TavilySearchResults imported (with deprecation warning expected)")
        
        # Test that our tools inherit from BaseTool correctly
        from pyframeworks_assistant.tools.tavily_tools import TavilySearchTool
        tool = TavilySearchTool(api_key="test")
        
        print(f"✅ Tool inheritance: {isinstance(tool, BaseTool)}")
        print(f"✅ Tool has required attributes: name={hasattr(tool, 'name')}, description={hasattr(tool, 'description')}")
        
        return True
    except Exception as e:
        print(f"❌ LangChain integration test failed: {e}")
        return False

def main():
    """Run comprehensive tests."""
    print("🚀 Running comprehensive GAAPF fixes verification...\n")
    
    tests = [
        ("Pydantic 2.x Compatibility", test_pydantic_settings),
        ("SystemConfig BaseSettings Fix", test_system_config),
        ("Tavily Tools Modernization", test_tavily_tools),
        ("All Tools Integration", test_all_tools),
        ("LangChain 0.3.x Integration", test_langchain_integration),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"{'='*60}")
        print(f"Running: {test_name}")
        print(f"{'='*60}")
        
        try:
            result = test_func()
            results.append((test_name, result))
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"\n{status}: {test_name}")
        except Exception as e:
            print(f"❌ CRASHED: {test_name} - {e}")
            results.append((test_name, False))
    
    print(f"\n{'='*60}")
    print("FINAL RESULTS")
    print(f"{'='*60}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\n📊 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! The fixes are working correctly.")
        print("\n✅ Summary of fixes:")
        print("   • BaseSettings import error fixed (pydantic -> pydantic-settings)")
        print("   • Pydantic 2.x compatibility implemented")
        print("   • Tavily tools modernized with LangChain 0.3.x patterns")
        print("   • All tools now work with Pydantic 2.x field definitions")
        print("   • Factory functions added for better tool integration")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
