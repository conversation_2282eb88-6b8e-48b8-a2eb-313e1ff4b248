# GAAPF Installation and Setup Guide

This guide will help you install and configure the GAAPF (Guidance AI Agent for Python Framework) system.

## Prerequisites

- **Python 3.10 or higher**
- **At least one LLM API key** (Google Gemini, OpenAI, or Anthropic Claude)
- **Git** (for cloning the repository)

## Installation Steps

### 1. Clone the Repository

```bash
git clone https://github.com/your-username/gaapf-guidance-ai-agent.git
cd gaapf-guidance-ai-agent
```

### 2. Create Virtual Environment (Recommended)

```bash
# Create virtual environment
python -m venv gaapf-env

# Activate virtual environment
# On Windows:
gaapf-env\Scripts\activate
# On macOS/Linux:
source gaapf-env/bin/activate
```

### 3. Install Dependencies

```bash
# Install core dependencies
pip install -r requirements.txt

# Or install in development mode (recommended for contributors)
pip install -e .

# Or install with optional dependencies
pip install -e ".[dev,viz]"
```

### 4. Environment Configuration

1. **Copy the example environment file:**
```bash
cp env.example .env
```

2. **Edit `.env` and add your API keys:**

```bash
# Choose at least one LLM provider
GOOGLE_API_KEY=your_google_gemini_api_key_here
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Optional: Tavily for enhanced search capabilities
TAVILY_API_KEY=your_tavily_api_key_here

# Optional: LangSmith for tracing and debugging
LANGCHAIN_TRACING_V2=true
LANGCHAIN_API_KEY=your_langsmith_api_key_here
LANGCHAIN_PROJECT=gaapf-guidance-ai-agent

# System Configuration (optional)
GAAPF_LOG_LEVEL=INFO
GAAPF_MAX_CONCURRENT_AGENTS=16
GAAPF_CONSTELLATION_TIMEOUT=300
```

## Getting API Keys

### Google Gemini API Key
1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Copy the key to your `.env` file

### OpenAI API Key
1. Go to [OpenAI API Keys](https://platform.openai.com/api-keys)
2. Create a new secret key
3. Copy the key to your `.env` file

### Anthropic Claude API Key
1. Go to [Anthropic Console](https://console.anthropic.com/)
2. Create an API key
3. Copy the key to your `.env` file

### Tavily API Key (Optional)
1. Go to [Tavily](https://tavily.com/)
2. Sign up and get your API key
3. Copy the key to your `.env` file

## Verification

### Test Installation

```bash
# Test basic import
python -c "from pyframeworks_assistant import LearningHubCore; print('✅ Installation successful!')"

# Check available LLM providers
python -c "from pyframeworks_assistant.interfaces.cli.llm_setup import get_available_providers; print('Available providers:', get_available_providers())"
```

### Test CLI Interface

```bash
# Run CLI with help
gaapf-cli --help

# Or run directly
python -m pyframeworks_assistant.interfaces.cli.main --help
```

### Test Streamlit Demo

```bash
# Launch Streamlit demo
streamlit run src/pyframeworks_assistant/interfaces/streamlit_demo.py
```

## Usage Examples

### CLI Interface

```bash
# Start interactive learning session
gaapf-cli

# Enable debug mode
gaapf-cli --debug
```

### Python API

```python
import asyncio
from pyframeworks_assistant import (
    LearningHubCore, 
    UserProfile, 
    SupportedFrameworks,
    SkillLevel,
    LearningStyle,
    LearningPace
)
from pyframeworks_assistant.interfaces.cli.llm_setup import setup_llm

async def main():
    # Setup LLM
    llm = setup_llm()
    if not llm:
        print("❌ No LLM provider available")
        return
    
    # Initialize learning hub
    hub = LearningHubCore(llm)
    await hub.initialize()
    
    # Create user profile
    profile = UserProfile(
        user_id="example_user",
        name="Example User",
        python_skill_level=SkillLevel.INTERMEDIATE,
        ai_ml_experience=SkillLevel.BEGINNER,
        preferred_learning_style=LearningStyle.HANDS_ON,
        learning_pace=LearningPace.MODERATE,
        learning_goals=["Learn LangChain", "Build RAG applications"]
    )
    
    # Start learning session
    session_id, session_info = await hub.start_learning_session(
        user_profile=profile,
        framework=SupportedFrameworks.LANGCHAIN,
        module_id="lc_basics"
    )
    
    print(f"✅ Session started: {session_id}")
    print(f"📊 Constellation: {session_info['constellation_type']}")
    
    # Process user message
    result = await hub.process_user_message(
        session_id=session_id,
        user_message="What is LangChain and how do I get started?"
    )
    
    print(f"🤖 Agent: {result['agent_role']}")
    print(f"💬 Response: {result['response']}")
    
    # End session
    summary = await hub.end_learning_session(session_id)
    print(f"📈 Session completed: {summary['duration_minutes']:.1f} minutes")

# Run the example
if __name__ == "__main__":
    asyncio.run(main())
```

## Troubleshooting

### Common Issues

#### 1. Import Errors
```bash
# Make sure you're in the correct directory and virtual environment
pip install -e .
```

#### 2. API Key Issues
```bash
# Check if API keys are loaded
python -c "from pyframeworks_assistant.config.system_config import system_config; print('Has LLM key:', system_config.has_llm_api_key())"
```

#### 3. LangChain/LangGraph Version Issues
```bash
# Update to latest versions
pip install --upgrade langchain langchain-core langchain-community langgraph
```

#### 4. Missing Dependencies
```bash
# Install specific provider dependencies
pip install langchain-google-genai  # For Google Gemini
pip install langchain-openai        # For OpenAI
pip install langchain-anthropic     # For Anthropic Claude
```

### Debug Mode

Enable debug mode for detailed logging:

```bash
# CLI with debug
gaapf-cli --debug

# Python with debug
import logging
logging.basicConfig(level=logging.DEBUG)
```

### Environment Variables

Check environment variables are loaded correctly:

```python
from pyframeworks_assistant.config.system_config import system_config
print("Google API Key:", "✅" if system_config.google_api_key else "❌")
print("OpenAI API Key:", "✅" if system_config.openai_api_key else "❌")
print("Anthropic API Key:", "✅" if system_config.anthropic_api_key else "❌")
print("Tavily API Key:", "✅" if system_config.tavily_api_key else "❌")
```

## Development Setup

### Additional Development Dependencies

```bash
# Install development dependencies
pip install -e ".[dev]"

# Install pre-commit hooks (optional)
pip install pre-commit
pre-commit install
```

### Code Quality Tools

```bash
# Format code
black src/

# Lint code
ruff check src/

# Type checking
mypy src/

# Run tests
pytest

# Run tests with coverage
pytest --cov=pyframeworks_assistant
```

### Project Structure

```
gaapf-guidance-ai-agent/
├── src/pyframeworks_assistant/    # Main package
│   ├── config/                    # Configuration and profiles
│   ├── core/                      # Core systems
│   ├── agents/                    # Agent implementations
│   ├── memory/                    # Memory systems
│   ├── tools/                     # Tool integrations
│   └── interfaces/                # User interfaces
├── tests/                         # Test suite
├── docs/                          # Documentation
├── requirements.txt               # Dependencies
├── pyproject.toml                # Project configuration
├── env.example                   # Environment template
└── README.md                     # Project overview
```

## Next Steps

1. **Complete the setup** by following this guide
2. **Run the CLI interface** to create your user profile
3. **Start a learning session** with your preferred framework
4. **Explore the Streamlit demo** for a visual interface
5. **Check out the documentation** for advanced usage

## Support

If you encounter issues:

1. Check this troubleshooting guide
2. Review the [main README](README.md) for architecture details
3. Open an issue on GitHub with:
   - Your Python version
   - Error messages
   - Steps to reproduce
   - Environment details

---

*Happy learning with GAAPF! 🚀*
