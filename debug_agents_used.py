#!/usr/bin/env python3
"""Debug script to find where agents_used is being set to integer 0."""

import sys
from pathlib import Path

# Add src to path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

import asyncio
from pyframeworks_assistant import (
    LearningHubCore,
    UserProfile,
    SupportedFrameworks,
    SkillLevel,
    LearningStyle,
    LearningPace,
)
from pyframeworks_assistant.interfaces.cli.llm_setup import setup_llm
from pyframeworks_assistant.core.constellation_types import ConstellationType

async def debug_agents_used():
    """Debug where agents_used is being set incorrectly."""
    print("🔍 Debugging agents_used issue...")
    
    # Setup
    llm = setup_llm()
    if not llm:
        print("❌ No LLM provider available")
        return
    
    hub = LearningHubCore(llm)
    await hub.initialize()
    
    # Create a simple user profile
    profile = UserProfile(
        user_id="debug_user",
        name="Debug User",
        programming_experience_years=2,
        python_skill_level=SkillLevel.INTERMEDIATE,
        ai_ml_experience=SkillLevel.BEGINNER,
        preferred_learning_style=LearningStyle.HANDS_ON,
        learning_pace=LearningPace.MODERATE,
        learning_goals=["Debug agents_used issue"]
    )
    
    print(f"✅ Profile created: {profile.name}")
    
    # Start a learning session
    session_id, session_info = await hub.start_learning_session(
        user_profile=profile,
        framework=SupportedFrameworks.LANGCHAIN,
        module_id="lc_basics"
    )
    
    print(f"✅ Session started: {session_id}")
    
    # Check constellation manager state
    if session_id in hub.constellation_manager.constellation_states:
        state = hub.constellation_manager.constellation_states[session_id]
        print(f"🔍 Constellation state handoff_chain: {state.handoff_chain}")
        print(f"🔍 Handoff chain type: {type(state.handoff_chain)}")
        print(f"🔍 Handoff chain length: {len(state.handoff_chain)}")
    
    # End the session and see what happens
    print("\n🔍 Ending session...")
    try:
        # First check what constellation manager returns
        constellation_summary = hub.constellation_manager.end_session(session_id)
        print(f"🔍 Constellation summary: {constellation_summary}")
        print(f"🔍 agents_used in summary: {constellation_summary.get('agents_used')} (type: {type(constellation_summary.get('agents_used'))})")
        
        # Now try the full end_learning_session
        summary = await hub.end_learning_session(session_id)
        print(f"✅ Session ended successfully: {summary}")
        
    except Exception as e:
        print(f"❌ Error ending session: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_agents_used())
