"""Tool manager for coordinating GAAPF tools and integrations."""

from typing import Dict, List, Optional, Any, Type
from langchain_core.tools import BaseTool

from .tavily_tools import TavilySearchTool, TavilyExtractTool, <PERSON>lyCrawlTool
from .file_tools import <PERSON><PERSON><PERSON>Tool, <PERSON>ReadTool, CodeExecuteTool
from .learning_tools import <PERSON><PERSON>ool, ProgressTool, ExerciseGeneratorTool
from ..config.system_config import system_config


class ToolManager:
    """Manages tools and integrations for GAAPF agents."""
    
    def __init__(self):
        """Initialize the tool manager."""
        self.available_tools: Dict[str, Type[BaseTool]] = {}
        self.tool_instances: Dict[str, BaseTool] = {}
        
        # Register available tools
        self._register_tools()
    
    def _register_tools(self) -> None:
        """Register all available tools."""
        
        # Search and discovery tools
        self.available_tools.update({
            "tavily_search": TavilySearchTool,
            "tavily_extract": <PERSON>lyExtractTool,
            "tavily_crawl": <PERSON><PERSON><PERSON>rawlTool,
        })
        
        # File and code tools
        self.available_tools.update({
            "file_write": <PERSON><PERSON><PERSON>Tool,
            "file_read": <PERSON><PERSON><PERSON>Tool,
            "code_execute": CodeExecuteTool,
        })
        
        # Learning and assessment tools
        self.available_tools.update({
            "assessment": AssessmentTool,
            "progress": ProgressTool,
            "exercise_generator": ExerciseGeneratorTool,
        })
    
    def get_tools_for_agent(self, agent_role: str) -> List[BaseTool]:
        """Get appropriate tools for a specific agent role.
        
        Args:
            agent_role: The role of the agent
            
        Returns:
            List of tools appropriate for the agent
        """
        tool_mapping = {
            "instructor": ["tavily_search", "exercise_generator"],
            "documentation_expert": ["tavily_search", "tavily_extract", "file_read"],
            "research_assistant": ["tavily_search", "tavily_extract", "tavily_crawl"],
            "knowledge_synthesizer": ["tavily_search", "file_read"],
            "code_assistant": ["file_write", "file_read", "code_execute", "tavily_search"],
            "practice_facilitator": ["exercise_generator", "file_write", "code_execute"],
            "project_guide": ["file_write", "file_read", "code_execute"],
            "troubleshooter": ["code_execute", "file_read", "tavily_search"],
            "mentor": ["progress", "assessment"],
            "motivational_coach": ["progress"],
            "assessment": ["assessment", "exercise_generator"],
            "progress_tracker": ["progress", "assessment"],
        }
        
        tool_names = tool_mapping.get(agent_role, [])
        tools = []
        
        for tool_name in tool_names:
            tool = self.get_tool(tool_name)
            if tool:
                tools.append(tool)
        
        return tools
    
    def get_tool(self, tool_name: str) -> Optional[BaseTool]:
        """Get a specific tool instance.
        
        Args:
            tool_name: Name of the tool
            
        Returns:
            Tool instance or None if not available
        """
        if tool_name in self.tool_instances:
            return self.tool_instances[tool_name]
        
        if tool_name not in self.available_tools:
            return None
        
        # Create tool instance
        tool_class = self.available_tools[tool_name]
        
        try:
            # Check if tool requires API keys
            if tool_name.startswith("tavily"):
                if not system_config.tavily_api_key:
                    print(f"Warning: {tool_name} requires Tavily API key")
                    return None
                tool_instance = tool_class(api_key=system_config.tavily_api_key)
            else:
                tool_instance = tool_class()
            
            self.tool_instances[tool_name] = tool_instance
            return tool_instance
        
        except Exception as e:
            print(f"Warning: Could not create tool {tool_name}: {e}")
            return None
    
    def get_all_tools(self) -> List[BaseTool]:
        """Get all available tool instances."""
        tools = []
        for tool_name in self.available_tools:
            tool = self.get_tool(tool_name)
            if tool:
                tools.append(tool)
        return tools
    
    def get_tools_by_category(self, category: str) -> List[BaseTool]:
        """Get tools by category.
        
        Args:
            category: Tool category (search, file, learning)
            
        Returns:
            List of tools in the category
        """
        category_mapping = {
            "search": ["tavily_search", "tavily_extract", "tavily_crawl"],
            "file": ["file_write", "file_read", "code_execute"],
            "learning": ["assessment", "progress", "exercise_generator"],
        }
        
        tool_names = category_mapping.get(category, [])
        tools = []
        
        for tool_name in tool_names:
            tool = self.get_tool(tool_name)
            if tool:
                tools.append(tool)
        
        return tools
    
    def is_tool_available(self, tool_name: str) -> bool:
        """Check if a tool is available.
        
        Args:
            tool_name: Name of the tool
            
        Returns:
            True if tool is available
        """
        return self.get_tool(tool_name) is not None
    
    def get_tool_info(self) -> Dict[str, Any]:
        """Get information about available tools."""
        info = {
            "total_tools": len(self.available_tools),
            "available_tools": [],
            "unavailable_tools": [],
            "categories": {
                "search": [],
                "file": [],
                "learning": []
            }
        }
        
        for tool_name in self.available_tools:
            tool = self.get_tool(tool_name)
            
            if tool:
                info["available_tools"].append({
                    "name": tool_name,
                    "description": tool.description,
                    "args": list(tool.args.keys()) if hasattr(tool, 'args') else []
                })
                
                # Categorize
                if tool_name.startswith("tavily"):
                    info["categories"]["search"].append(tool_name)
                elif tool_name.startswith("file") or tool_name == "code_execute":
                    info["categories"]["file"].append(tool_name)
                else:
                    info["categories"]["learning"].append(tool_name)
            else:
                info["unavailable_tools"].append(tool_name)
        
        return info
