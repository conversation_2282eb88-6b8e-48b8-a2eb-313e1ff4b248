"""LLM setup and configuration for CLI interface."""

from typing import Optional
from langchain_core.language_models import BaseChatModel
from rich.console import Console

from ...config.system_config import system_config

console = Console()


def setup_llm() -> Optional[BaseChatModel]:
    """Setup and return the appropriate LLM based on available API keys."""
    
    # Try Google Gemini first
    if system_config.google_api_key:
        try:
            from langchain_google_genai import ChatGoogleGenerativeAI
            
            llm = ChatGoogleGenerativeAI(
                model="gemini-pro",
                google_api_key=system_config.google_api_key,
                temperature=0.7,
                convert_system_message_to_human=True
            )
            
            console.print("[green]✅ Using Google Gemini Pro[/green]")
            return llm
        
        except ImportError:
            console.print("[yellow]⚠️ Google Gemini not available (missing langchain-google-genai)[/yellow]")
        except Exception as e:
            console.print(f"[yellow]⚠️ Google Gemini setup failed: {e}[/yellow]")
    
    # Try OpenAI
    if system_config.openai_api_key:
        try:
            from langchain_openai import ChatOpenAI
            
            llm = ChatOpenAI(
                model="gpt-3.5-turbo",
                openai_api_key=system_config.openai_api_key,
                temperature=0.7
            )
            
            console.print("[green]✅ Using OpenAI GPT-3.5-turbo[/green]")
            return llm
        
        except ImportError:
            console.print("[yellow]⚠️ OpenAI not available (missing langchain-openai)[/yellow]")
        except Exception as e:
            console.print(f"[yellow]⚠️ OpenAI setup failed: {e}[/yellow]")
    
    # Try Anthropic Claude
    if system_config.anthropic_api_key:
        try:
            from langchain_anthropic import ChatAnthropic
            
            llm = ChatAnthropic(
                model="claude-3-sonnet-20240229",
                anthropic_api_key=system_config.anthropic_api_key,
                temperature=0.7
            )
            
            console.print("[green]✅ Using Anthropic Claude 3 Sonnet[/green]")
            return llm
        
        except ImportError:
            console.print("[yellow]⚠️ Anthropic not available (missing langchain-anthropic)[/yellow]")
        except Exception as e:
            console.print(f"[yellow]⚠️ Anthropic setup failed: {e}[/yellow]")
    
    console.print("[red]❌ No LLM providers available[/red]")
    return None


def get_available_providers() -> list[str]:
    """Get list of available LLM providers."""
    providers = []
    
    if system_config.google_api_key:
        try:
            import langchain_google_genai
            providers.append("Google Gemini")
        except ImportError:
            pass
    
    if system_config.openai_api_key:
        try:
            import langchain_openai
            providers.append("OpenAI")
        except ImportError:
            pass
    
    if system_config.anthropic_api_key:
        try:
            import langchain_anthropic
            providers.append("Anthropic")
        except ImportError:
            pass
    
    return providers
