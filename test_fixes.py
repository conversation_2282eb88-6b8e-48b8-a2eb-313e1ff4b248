#!/usr/bin/env python3
"""Test script to verify the Pydantic and Tavily fixes."""

import sys
import os
from pathlib import Path

# Add src to path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def test_system_config():
    """Test that SystemConfig loads correctly with pydantic-settings."""
    print("🧪 Testing SystemConfig...")
    try:
        from pyframeworks_assistant.config.system_config import SystemConfig, system_config
        print("✅ SystemConfig imported successfully")
        
        # Test creating a new instance
        config = SystemConfig()
        print(f"✅ SystemConfig instance created - Log level: {config.log_level}")
        
        # Test global instance
        print(f"✅ Global config works - Debug: {system_config.debug}")
        
        return True
    except Exception as e:
        print(f"❌ SystemConfig test failed: {e}")
        return False

def test_tavily_tools():
    """Test that Tavily tools load correctly with Pydantic 2.x."""
    print("\n🧪 Testing Tavily tools...")
    try:
        from pyframeworks_assistant.tools.tavily_tools import (
            <PERSON>ly<PERSON>earchTool, 
            TavilyExtractTool, 
            TavilyCrawlTool,
            create_tavily_search_tool,
            get_tavily_tools
        )
        print("✅ Tavily tools imported successfully")
        
        # Test tool creation (without API key for now)
        try:
            tool = TavilySearchTool(api_key="test_key")
            print(f"✅ TavilySearchTool created - Name: {tool.name}")
        except Exception as e:
            print(f"⚠️  TavilySearchTool creation failed (expected without real API key): {e}")
        
        return True
    except Exception as e:
        print(f"❌ Tavily tools test failed: {e}")
        return False

def test_tool_manager():
    """Test that ToolManager loads correctly."""
    print("\n🧪 Testing ToolManager...")
    try:
        from pyframeworks_assistant.tools.tool_manager import ToolManager
        print("✅ ToolManager imported successfully")
        
        # Test creating tool manager
        tm = ToolManager()
        print(f"✅ ToolManager created - Available tools: {len(tm.available_tools)}")
        print(f"   Tools: {list(tm.available_tools.keys())}")
        
        return True
    except Exception as e:
        print(f"❌ ToolManager test failed: {e}")
        return False

def test_pydantic_compatibility():
    """Test Pydantic 2.x compatibility."""
    print("\n🧪 Testing Pydantic 2.x compatibility...")
    try:
        import pydantic
        from pydantic_settings import BaseSettings
        from pydantic import BaseModel, Field
        
        print(f"✅ Pydantic version: {pydantic.VERSION}")
        print("✅ pydantic-settings imported successfully")
        
        # Test a simple BaseSettings model
        class TestConfig(BaseSettings):
            test_field: str = Field("default", alias="TEST_FIELD")
            model_config = {
                "env_file": ".env",
                "case_sensitive": False
            }
        
        config = TestConfig()
        print(f"✅ Test BaseSettings model works: {config.test_field}")
        
        return True
    except Exception as e:
        print(f"❌ Pydantic compatibility test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Starting GAAPF fixes verification...\n")
    
    tests = [
        test_pydantic_compatibility,
        test_system_config,
        test_tavily_tools,
        test_tool_manager,
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            results.append(False)
    
    print(f"\n📊 Test Results: {sum(results)}/{len(results)} passed")
    
    if all(results):
        print("🎉 All tests passed! The fixes are working correctly.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    return all(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
