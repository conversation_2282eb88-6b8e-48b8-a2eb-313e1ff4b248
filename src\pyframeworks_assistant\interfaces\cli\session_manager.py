"""Session management for CLI interface."""

import json
from typing import Dict, List, Optional, Any
from pathlib import Path
from datetime import datetime, timedelta
from rich.console import Console

from ...config.system_config import system_config

console = Console()


class SessionRecord:
    """Represents a learning session record."""
    
    def __init__(
        self,
        session_id: str,
        user_id: str,
        framework: str,
        module_id: str,
        constellation_type: str,
        start_time: datetime,
        end_time: Optional[datetime] = None,
        duration_minutes: float = 0.0,
        interactions_count: int = 0,
        satisfaction_score: float = 0.0,
        completion_estimate: float = 0.0
    ):
        self.session_id = session_id
        self.user_id = user_id
        self.framework = framework
        self.module_id = module_id
        self.constellation_type = constellation_type
        self.start_time = start_time
        self.end_time = end_time
        self.duration_minutes = duration_minutes
        self.interactions_count = interactions_count
        self.satisfaction_score = satisfaction_score
        self.completion_estimate = completion_estimate
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "session_id": self.session_id,
            "user_id": self.user_id,
            "framework": self.framework,
            "module_id": self.module_id,
            "constellation_type": self.constellation_type,
            "start_time": self.start_time.isoformat(),
            "end_time": self.end_time.isoformat() if self.end_time else None,
            "duration_minutes": self.duration_minutes,
            "interactions_count": self.interactions_count,
            "satisfaction_score": self.satisfaction_score,
            "completion_estimate": self.completion_estimate
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SessionRecord':
        """Create from dictionary."""
        return cls(
            session_id=data["session_id"],
            user_id=data["user_id"],
            framework=data["framework"],
            module_id=data["module_id"],
            constellation_type=data["constellation_type"],
            start_time=datetime.fromisoformat(data["start_time"]),
            end_time=datetime.fromisoformat(data["end_time"]) if data["end_time"] else None,
            duration_minutes=data.get("duration_minutes", 0.0),
            interactions_count=data.get("interactions_count", 0),
            satisfaction_score=data.get("satisfaction_score", 0.0),
            completion_estimate=data.get("completion_estimate", 0.0)
        )


class SessionManager:
    """Manages learning sessions for the CLI interface."""
    
    def __init__(self):
        """Initialize the session manager."""
        self.sessions_dir = Path("sessions")
        self.sessions_dir.mkdir(exist_ok=True)
        
        # In-memory cache of recent sessions
        self.session_cache: Dict[str, SessionRecord] = {}
        self._load_recent_sessions()
    
    def _load_recent_sessions(self) -> None:
        """Load recent sessions into cache."""
        try:
            sessions_file = self.sessions_dir / "sessions.json"
            if sessions_file.exists():
                with open(sessions_file, 'r') as f:
                    sessions_data = json.load(f)
                
                for session_data in sessions_data[-50:]:  # Load last 50 sessions
                    session = SessionRecord.from_dict(session_data)
                    self.session_cache[session.session_id] = session
        
        except Exception as e:
            console.print(f"[yellow]⚠️ Could not load session history: {e}[/yellow]")
    
    def start_session(
        self,
        session_id: str,
        user_id: str,
        framework: str,
        module_id: str,
        constellation_type: str
    ) -> SessionRecord:
        """Start a new learning session.
        
        Args:
            session_id: Session identifier
            user_id: User identifier
            framework: Framework being learned
            module_id: Module within framework
            constellation_type: Type of constellation
            
        Returns:
            Session record
        """
        session = SessionRecord(
            session_id=session_id,
            user_id=user_id,
            framework=framework,
            module_id=module_id,
            constellation_type=constellation_type,
            start_time=datetime.now()
        )
        
        self.session_cache[session_id] = session
        return session
    
    def end_session(
        self,
        session_id: str,
        duration_minutes: float,
        interactions_count: int,
        satisfaction_score: float = 0.0,
        completion_estimate: float = 0.0
    ) -> Optional[SessionRecord]:
        """End a learning session.
        
        Args:
            session_id: Session identifier
            duration_minutes: Session duration
            interactions_count: Number of interactions
            satisfaction_score: User satisfaction (0-1)
            completion_estimate: Estimated completion (0-1)
            
        Returns:
            Updated session record or None if not found
        """
        if session_id not in self.session_cache:
            return None
        
        session = self.session_cache[session_id]
        session.end_time = datetime.now()
        session.duration_minutes = duration_minutes
        session.interactions_count = interactions_count
        session.satisfaction_score = satisfaction_score
        session.completion_estimate = completion_estimate
        
        # Persist session
        self._persist_session(session)
        
        return session
    
    def _persist_session(self, session: SessionRecord) -> None:
        """Persist session to storage."""
        try:
            sessions_file = self.sessions_dir / "sessions.json"
            
            # Load existing sessions
            sessions_data = []
            if sessions_file.exists():
                with open(sessions_file, 'r') as f:
                    sessions_data = json.load(f)
            
            # Add new session
            sessions_data.append(session.to_dict())
            
            # Keep only last 1000 sessions
            if len(sessions_data) > 1000:
                sessions_data = sessions_data[-1000:]
            
            # Save back to file
            with open(sessions_file, 'w') as f:
                json.dump(sessions_data, f, indent=2)
        
        except Exception as e:
            console.print(f"[yellow]⚠️ Could not persist session: {e}[/yellow]")
    
    def get_session(self, session_id: str) -> Optional[SessionRecord]:
        """Get a session record.
        
        Args:
            session_id: Session identifier
            
        Returns:
            Session record or None if not found
        """
        return self.session_cache.get(session_id)
    
    def get_user_sessions(self, user_id: str, limit: int = 10) -> List[SessionRecord]:
        """Get sessions for a specific user.
        
        Args:
            user_id: User identifier
            limit: Maximum number of sessions to return
            
        Returns:
            List of session records
        """
        user_sessions = [
            session for session in self.session_cache.values()
            if session.user_id == user_id
        ]
        
        # Sort by start time (most recent first)
        user_sessions.sort(key=lambda s: s.start_time, reverse=True)
        
        return user_sessions[:limit]
    
    def get_session_stats(self, user_id: str) -> Dict[str, Any]:
        """Get session statistics for a user.
        
        Args:
            user_id: User identifier
            
        Returns:
            Session statistics
        """
        user_sessions = [
            session for session in self.session_cache.values()
            if session.user_id == user_id and session.end_time
        ]
        
        if not user_sessions:
            return {
                "total_sessions": 0,
                "total_time_minutes": 0,
                "average_duration": 0,
                "average_satisfaction": 0,
                "average_completion": 0,
                "frameworks_used": [],
                "constellation_preferences": {}
            }
        
        total_time = sum(s.duration_minutes for s in user_sessions)
        avg_duration = total_time / len(user_sessions)
        avg_satisfaction = sum(s.satisfaction_score for s in user_sessions) / len(user_sessions)
        avg_completion = sum(s.completion_estimate for s in user_sessions) / len(user_sessions)
        
        # Framework usage
        frameworks = list(set(s.framework for s in user_sessions))
        
        # Constellation preferences
        constellation_counts = {}
        constellation_satisfaction = {}
        
        for session in user_sessions:
            const_type = session.constellation_type
            if const_type not in constellation_counts:
                constellation_counts[const_type] = 0
                constellation_satisfaction[const_type] = []
            
            constellation_counts[const_type] += 1
            constellation_satisfaction[const_type].append(session.satisfaction_score)
        
        constellation_preferences = {}
        for const_type, satisfactions in constellation_satisfaction.items():
            avg_satisfaction_for_type = sum(satisfactions) / len(satisfactions)
            constellation_preferences[const_type] = {
                "usage_count": constellation_counts[const_type],
                "average_satisfaction": avg_satisfaction_for_type
            }
        
        return {
            "total_sessions": len(user_sessions),
            "total_time_minutes": total_time,
            "average_duration": avg_duration,
            "average_satisfaction": avg_satisfaction,
            "average_completion": avg_completion,
            "frameworks_used": frameworks,
            "constellation_preferences": constellation_preferences
        }
    
    def cleanup_old_sessions(self, days_threshold: int = 30) -> None:
        """Clean up old session data.
        
        Args:
            days_threshold: Number of days after which to clean up
        """
        cutoff_date = datetime.now() - timedelta(days=days_threshold)
        
        # Remove old sessions from cache
        to_remove = []
        for session_id, session in self.session_cache.items():
            if session.start_time < cutoff_date:
                to_remove.append(session_id)
        
        for session_id in to_remove:
            del self.session_cache[session_id]
        
        console.print(f"[green]✅ Cleaned up {len(to_remove)} old sessions[/green]")
