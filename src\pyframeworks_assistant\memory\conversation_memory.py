"""Conversation memory for session context management."""

import json
import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from pathlib import Path
from pydantic import BaseModel, Field


class ConversationTurn(BaseModel):
    """Represents a single turn in conversation."""
    
    timestamp: datetime
    speaker: str  # "user" or agent role
    message: str
    metadata: Dict[str, Any] = Field(default_factory=dict)


class ConversationSession(BaseModel):
    """Represents a complete conversation session."""
    
    session_id: str
    user_id: str
    start_time: datetime
    end_time: Optional[datetime] = None
    turns: List[ConversationTurn] = Field(default_factory=list)
    summary: Optional[str] = None
    
    def add_turn(self, speaker: str, message: str, timestamp: Optional[datetime] = None) -> None:
        """Add a conversation turn."""
        turn = ConversationTurn(
            timestamp=timestamp or datetime.now(),
            speaker=speaker,
            message=message
        )
        self.turns.append(turn)
    
    def get_recent_turns(self, count: int = 10) -> List[ConversationTurn]:
        """Get recent conversation turns."""
        return self.turns[-count:] if self.turns else []


class ConversationMemory:
    """Manages conversation memory for learning sessions."""
    
    def __init__(self, data_dir: Path):
        """Initialize conversation memory.
        
        Args:
            data_dir: Directory for storing conversation data
        """
        self.data_dir = data_dir
        self.data_dir.mkdir(exist_ok=True)
        
        # Active sessions
        self.active_sessions: Dict[str, ConversationSession] = {}
        
        # Archived sessions (in memory cache)
        self.archived_sessions: Dict[str, ConversationSession] = {}
        
        self.is_initialized = False
    
    async def initialize(self) -> None:
        """Initialize conversation memory system."""
        if self.is_initialized:
            return
        
        # Load recent archived sessions
        await self._load_recent_sessions()
        
        self.is_initialized = True
    
    async def _load_recent_sessions(self) -> None:
        """Load recent conversation sessions from storage."""
        try:
            # Load sessions from the last 7 days
            cutoff_date = datetime.now() - timedelta(days=7)
            
            for session_file in self.data_dir.glob("session_*.json"):
                try:
                    with open(session_file, 'r') as f:
                        session_data = json.load(f)
                    
                    session = ConversationSession(**session_data)
                    
                    # Only load recent sessions
                    if session.start_time >= cutoff_date:
                        self.archived_sessions[session.session_id] = session
                
                except Exception as e:
                    print(f"Warning: Could not load session file {session_file}: {e}")
        
        except Exception as e:
            print(f"Warning: Could not load conversation sessions: {e}")
    
    async def start_session(self, session_id: str, user_id: str) -> None:
        """Start a new conversation session.
        
        Args:
            session_id: Session identifier
            user_id: User identifier
        """
        session = ConversationSession(
            session_id=session_id,
            user_id=user_id,
            start_time=datetime.now()
        )
        
        self.active_sessions[session_id] = session
    
    async def add_user_message(
        self,
        session_id: str,
        message: str,
        timestamp: Optional[datetime] = None
    ) -> None:
        """Add a user message to the conversation.
        
        Args:
            session_id: Session identifier
            message: User's message
            timestamp: Message timestamp
        """
        if session_id in self.active_sessions:
            self.active_sessions[session_id].add_turn("user", message, timestamp)
    
    async def add_agent_response(
        self,
        session_id: str,
        response: str,
        agent_role: Optional[str] = None,
        timestamp: Optional[datetime] = None
    ) -> None:
        """Add an agent response to the conversation.
        
        Args:
            session_id: Session identifier
            response: Agent's response
            agent_role: Role of the responding agent
            timestamp: Response timestamp
        """
        if session_id in self.active_sessions:
            speaker = agent_role or "agent"
            self.active_sessions[session_id].add_turn(speaker, response, timestamp)
    
    async def get_session_history(
        self,
        session_id: str,
        turn_limit: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """Get conversation history for a session.
        
        Args:
            session_id: Session identifier
            turn_limit: Maximum number of turns to return
            
        Returns:
            List of conversation turns
        """
        session = None
        
        # Check active sessions first
        if session_id in self.active_sessions:
            session = self.active_sessions[session_id]
        # Check archived sessions
        elif session_id in self.archived_sessions:
            session = self.archived_sessions[session_id]
        
        if not session:
            return []
        
        turns = session.turns
        if turn_limit:
            turns = turns[-turn_limit:]
        
        return [
            {
                "timestamp": turn.timestamp.isoformat(),
                "speaker": turn.speaker,
                "message": turn.message,
                "metadata": turn.metadata
            }
            for turn in turns
        ]
    
    async def get_conversation_context(
        self,
        session_id: str,
        context_turns: int = 5
    ) -> str:
        """Get conversation context as formatted string.
        
        Args:
            session_id: Session identifier
            context_turns: Number of recent turns to include
            
        Returns:
            Formatted conversation context
        """
        history = await self.get_session_history(session_id, context_turns)
        
        if not history:
            return ""
        
        context_lines = []
        for turn in history:
            speaker = turn["speaker"].title()
            message = turn["message"]
            context_lines.append(f"{speaker}: {message}")
        
        return "\n".join(context_lines)
    
    async def archive_session(self, session_id: str) -> None:
        """Archive a conversation session.
        
        Args:
            session_id: Session to archive
        """
        if session_id not in self.active_sessions:
            return
        
        session = self.active_sessions[session_id]
        session.end_time = datetime.now()
        
        # Generate session summary
        session.summary = await self._generate_session_summary(session)
        
        # Save to file
        session_file = self.data_dir / f"session_{session_id}.json"
        with open(session_file, 'w') as f:
            json.dump(session.dict(), f, indent=2, default=str)
        
        # Move to archived sessions
        self.archived_sessions[session_id] = session
        del self.active_sessions[session_id]
    
    async def _generate_session_summary(self, session: ConversationSession) -> str:
        """Generate a summary of the conversation session."""
        if not session.turns:
            return "Empty session"
        
        # Simple summary based on turn count and duration
        duration = (session.end_time - session.start_time).total_seconds() / 60
        turn_count = len(session.turns)
        
        # Count agent types involved
        agents_involved = set()
        for turn in session.turns:
            if turn.speaker != "user":
                agents_involved.add(turn.speaker)
        
        summary_parts = [
            f"Session lasted {duration:.1f} minutes",
            f"{turn_count} conversation turns",
            f"Agents involved: {', '.join(agents_involved) if agents_involved else 'None'}"
        ]
        
        return "; ".join(summary_parts)
    
    async def get_user_conversation_patterns(self, user_id: str) -> Dict[str, Any]:
        """Analyze conversation patterns for a user.
        
        Args:
            user_id: User identifier
            
        Returns:
            Analysis of user's conversation patterns
        """
        user_sessions = []
        
        # Collect all sessions for user
        for session in self.active_sessions.values():
            if session.user_id == user_id:
                user_sessions.append(session)
        
        for session in self.archived_sessions.values():
            if session.user_id == user_id:
                user_sessions.append(session)
        
        if not user_sessions:
            return {"error": "No conversation data found"}
        
        # Analyze patterns
        total_turns = sum(len(session.turns) for session in user_sessions)
        total_user_messages = sum(
            len([turn for turn in session.turns if turn.speaker == "user"])
            for session in user_sessions
        )
        
        # Average message length
        user_messages = []
        for session in user_sessions:
            user_messages.extend([
                turn.message for turn in session.turns if turn.speaker == "user"
            ])
        
        avg_message_length = (
            sum(len(msg.split()) for msg in user_messages) / len(user_messages)
            if user_messages else 0
        )
        
        # Most active agents
        agent_interactions = {}
        for session in user_sessions:
            for turn in session.turns:
                if turn.speaker != "user":
                    agent_interactions[turn.speaker] = agent_interactions.get(turn.speaker, 0) + 1
        
        return {
            "user_id": user_id,
            "total_sessions": len(user_sessions),
            "total_conversation_turns": total_turns,
            "total_user_messages": total_user_messages,
            "average_message_length_words": avg_message_length,
            "most_interacted_agents": sorted(
                agent_interactions.items(), key=lambda x: x[1], reverse=True
            )[:5],
            "conversation_activity": "high" if total_user_messages > 50 else "moderate" if total_user_messages > 20 else "low"
        }
    
    async def get_user_summary(self, user_id: str) -> Dict[str, Any]:
        """Get conversation memory summary for a user."""
        patterns = await self.get_user_conversation_patterns(user_id)
        
        return {
            "system": "conversation_memory",
            "user_patterns": patterns,
            "active_sessions": len([
                s for s in self.active_sessions.values() if s.user_id == user_id
            ]),
            "archived_sessions": len([
                s for s in self.archived_sessions.values() if s.user_id == user_id
            ])
        }
    
    async def cleanup_old_data(self, cutoff_date: datetime) -> None:
        """Clean up old conversation data.
        
        Args:
            cutoff_date: Date before which to remove data
        """
        # Remove old archived sessions from memory
        to_remove = []
        for session_id, session in self.archived_sessions.items():
            if session.start_time < cutoff_date:
                to_remove.append(session_id)
        
        for session_id in to_remove:
            del self.archived_sessions[session_id]
        
        # Remove old session files
        for session_file in self.data_dir.glob("session_*.json"):
            try:
                with open(session_file, 'r') as f:
                    session_data = json.load(f)
                
                start_time = datetime.fromisoformat(session_data["start_time"])
                if start_time < cutoff_date:
                    session_file.unlink()
            
            except Exception as e:
                print(f"Warning: Could not process session file {session_file}: {e}")
    
    def get_active_sessions(self) -> List[str]:
        """Get list of active session IDs."""
        return list(self.active_sessions.keys())
