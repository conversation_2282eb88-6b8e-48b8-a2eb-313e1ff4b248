#!/usr/bin/env python3
"""Test constellation creation to debug the LangGraph edge issue."""

import sys
from pathlib import Path

# Add src to path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

import asyncio
from pyframeworks_assistant import (
    LearningHubCore,
    UserProfile,
    SupportedFrameworks,
    SkillLevel,
    LearningStyle,
    LearningPace,
)
from pyframeworks_assistant.interfaces.cli.llm_setup import setup_llm
from pyframeworks_assistant.core.constellation_types import get_available_constellations, get_constellation_config

async def test_constellation_creation():
    """Test creating a constellation to debug the edge issue."""
    print("🧪 Testing constellation creation...")
    
    # Setup
    llm = setup_llm()
    if not llm:
        print("❌ No LLM provider available")
        return
    
    hub = LearningHubCore(llm)
    await hub.initialize()
    
    # Create a simple user profile
    profile = UserProfile(
        user_id="test_user",
        name="Test User",
        programming_experience_years=2,
        python_skill_level=SkillLevel.INTERMEDIATE,
        ai_ml_experience=SkillLevel.BEGINNER,
        preferred_learning_style=LearningStyle.HANDS_ON,
        learning_pace=LearningPace.MODERATE,
        learning_goals=["Test constellation creation"]
    )
    
    print(f"✅ Profile created: {profile.name}")
    
    # Test each constellation type
    for constellation_type in get_available_constellations():
        print(f"\n🌟 Testing {constellation_type.value}...")
        
        try:
            config = get_constellation_config(constellation_type)
            print(f"   Config: {config.name}")
            print(f"   Primary agents: {config.primary_agents}")
            print(f"   Support agents: {config.support_agents}")
            
            # Try to create constellation directly
            constellation = await hub.constellation_manager.create_constellation(
                constellation_type=constellation_type,
                user_profile=profile,
                framework=SupportedFrameworks.LANGCHAIN,
                module_id="lc_basics",
                session_id=f"test_{constellation_type.value}"
            )
            
            print(f"   ✅ Constellation created successfully")
            
            # Clean up
            hub.constellation_manager.end_session(f"test_{constellation_type.value}")
            
        except Exception as e:
            print(f"   ❌ Failed to create constellation: {e}")
            import traceback
            traceback.print_exc()
            break

if __name__ == "__main__":
    asyncio.run(test_constellation_creation())
