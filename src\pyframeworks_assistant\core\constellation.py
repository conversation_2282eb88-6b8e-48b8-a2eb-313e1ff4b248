"""Constellation manager for GAAPF adaptive learning system."""

import asyncio
from typing import Dict, List, Optional, Any, Tu<PERSON>
from datetime import datetime
from pydantic import BaseModel, Field
from langchain_core.language_models import BaseChatModel
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage
from langgraph.graph import StateGraph, END
from langgraph.checkpoint.sqlite import SqliteSaver

from ..config.user_profiles import UserProfile
from ..config.framework_configs import SupportedFrameworks
from .constellation_types import ConstellationType, get_constellation_config
from ..agents.base_agent import BaseAgent, AgentRole, AgentResponse
from ..agents.agent_factory import AgentFactory


class ConstellationState(BaseModel):
    """State management for constellation workflow."""
    
    # Session information
    session_id: str
    user_id: str
    framework: SupportedFrameworks
    module_id: str
    constellation_type: ConstellationType
    
    # Current conversation
    user_message: str = ""
    current_agent: Optional[AgentRole] = None
    conversation_history: List[BaseMessage] = Field(default_factory=list)
    
    # Agent responses and handoffs
    agent_responses: List[AgentResponse] = Field(default_factory=list)
    handoff_chain: List[AgentRole] = Field(default_factory=list)
    
    # Learning context
    user_profile: Optional[UserProfile] = None
    learning_context: Dict[str, Any] = Field(default_factory=dict)
    
    # Session tracking
    session_start_time: datetime = Field(default_factory=datetime.now)
    total_interactions: int = 0
    active_agents: List[AgentRole] = Field(default_factory=list)
    
    model_config = {
        "arbitrary_types_allowed": True
    }


class ConstellationManager:
    """Manager for creating and orchestrating agent constellations using LangGraph."""
    
    def __init__(self, llm: BaseChatModel, tools: Optional[Dict[str, List[Any]]] = None):
        """Initialize the constellation manager.
        
        Args:
            llm: Language model for agents
            tools: Optional tools for agents
        """
        self.llm = llm
        self.agent_factory = AgentFactory(llm, tools)
        self.active_constellations: Dict[str, StateGraph] = {}
        self.constellation_states: Dict[str, ConstellationState] = {}
        
        # Setup checkpointing for state persistence
        self.checkpointer = SqliteSaver.from_conn_string(":memory:")
    
    async def create_constellation(
        self,
        constellation_type: ConstellationType,
        user_profile: UserProfile,
        framework: SupportedFrameworks,
        module_id: str,
        session_id: str
    ) -> StateGraph:
        """Create a new constellation with specified configuration.
        
        Args:
            constellation_type: Type of constellation to create
            user_profile: User profile for personalization
            framework: Framework being learned
            module_id: Specific module within framework
            session_id: Unique session identifier
            
        Returns:
            Configured LangGraph StateGraph
        """
        # Get constellation configuration
        config = get_constellation_config(constellation_type)
        
        # Create constellation state
        state = ConstellationState(
            session_id=session_id,
            user_id=user_profile.user_id,
            framework=framework,
            module_id=module_id,
            constellation_type=constellation_type,
            user_profile=user_profile,
            active_agents=[AgentRole(role) for role in config.primary_agents + config.support_agents]
        )
        
        # Create agents for this constellation
        all_agent_roles = [AgentRole(role) for role in config.primary_agents + config.support_agents]
        constellation_agents = self.agent_factory.create_constellation_agents(all_agent_roles)
        
        # Create LangGraph workflow
        workflow = self._create_workflow(config, constellation_agents, state)
        
        # Store constellation
        self.active_constellations[session_id] = workflow
        self.constellation_states[session_id] = state
        
        return workflow
    
    def _create_workflow(
        self,
        config,
        agents: Dict[AgentRole, BaseAgent],
        initial_state: ConstellationState
    ) -> StateGraph:
        """Create LangGraph workflow for the constellation."""
        
        # Define the graph state
        workflow = StateGraph(ConstellationState)
        
        # Add agent nodes
        for role, agent in agents.items():
            workflow.add_node(role.value, self._create_agent_node(agent))
        
        # Add routing node for intelligent handoffs
        workflow.add_node("router", self._create_router_node(config, agents))
        
        # Set entry point
        workflow.set_entry_point("router")
        
        # Add edges based on constellation configuration
        self._add_constellation_edges(workflow, config)
        
        # Compile with checkpointing
        return workflow.compile(checkpointer=self.checkpointer)
    
    def _create_agent_node(self, agent: BaseAgent):
        """Create a node function for an agent."""
        
        async def agent_node(state: ConstellationState) -> ConstellationState:
            """Process message with the agent."""
            
            # Process the user message
            response = await agent.process_message(
                message=state.user_message,
                context={
                    "framework": state.framework.value,
                    "module_id": state.module_id,
                    "user_profile": state.user_profile.dict() if state.user_profile else {},
                    "learning_context": state.learning_context
                },
                conversation_history=state.conversation_history
            )
            
            # Update state
            state.agent_responses.append(response)
            state.current_agent = agent.role
            state.handoff_chain.append(agent.role)
            state.total_interactions += 1
            
            # Add response to conversation history
            state.conversation_history.append(AIMessage(content=response.content))
            
            return state
        
        return agent_node
    
    def _create_router_node(self, config, agents: Dict[AgentRole, BaseAgent]):
        """Create the routing node for intelligent handoffs."""
        
        async def router_node(state: ConstellationState) -> ConstellationState:
            """Route to the appropriate agent based on context and handoff logic."""
            
            # If this is the first interaction, start with primary agent
            if not state.handoff_chain:
                primary_agent_role = AgentRole(config.primary_agents[0])
                state.current_agent = primary_agent_role
                return state
            
            # Check if last agent suggested a handoff
            if state.agent_responses:
                last_response = state.agent_responses[-1]
                if (last_response.handoff_decision and 
                    last_response.handoff_decision.should_handoff and
                    last_response.handoff_decision.next_agent):
                    
                    next_agent_role = AgentRole(last_response.handoff_decision.next_agent)
                    if next_agent_role in agents:
                        state.current_agent = next_agent_role
                        return state
            
            # Default to continuing with current agent or primary agent
            if state.current_agent:
                return state
            else:
                state.current_agent = AgentRole(config.primary_agents[0])
                return state
        
        return router_node
    
    def _add_constellation_edges(self, workflow: StateGraph, config):
        """Add edges to the workflow based on constellation configuration."""

        # Create a single conditional edge from router to all agents
        all_agents = config.primary_agents + config.support_agents

        def router_condition(state):
            """Determine where to route from the router node."""
            # Check if we should end the conversation
            if len(state.agent_responses) > 10:
                return END

            # Route to current agent if set
            if state.current_agent:
                agent_value = state.current_agent.value if hasattr(state.current_agent, 'value') else str(state.current_agent)
                # Ensure the agent is in our available agents
                if agent_value in all_agents:
                    return agent_value

            # Default to first primary agent
            return config.primary_agents[0]

        # Create mapping for all possible destinations
        agent_mapping = {}
        for agent_role in all_agents:
            agent_mapping[agent_role] = agent_role
        agent_mapping[END] = END

        # Add single conditional edge with all possible destinations
        workflow.add_conditional_edges(
            "router",
            router_condition,
            agent_mapping
        )

        # Agents back to router for potential handoffs
        for agent_role in all_agents:
            workflow.add_edge(agent_role, "router")
    
    async def run_session(
        self,
        session_id: str,
        user_message: str,
        user_profile: UserProfile,
        framework: SupportedFrameworks,
        module_id: str
    ) -> Dict[str, Any]:
        """Run a learning session with the constellation.
        
        Args:
            session_id: Session identifier
            user_message: User's message/question
            user_profile: User profile for context
            framework: Framework being learned
            module_id: Module within framework
            
        Returns:
            Session result with agent response and metadata
        """
        if session_id not in self.active_constellations:
            raise ValueError(f"No active constellation found for session {session_id}")
        
        workflow = self.active_constellations[session_id]
        state = self.constellation_states[session_id]
        
        # Update state with new message
        state.user_message = user_message
        state.conversation_history.append(HumanMessage(content=user_message))
        
        # Run the workflow
        result = await workflow.ainvoke(
            state,
            config={"configurable": {"thread_id": session_id}}
        )
        
        # Update stored state
        self.constellation_states[session_id] = result
        
        # Return the latest agent response
        if result.agent_responses:
            latest_response = result.agent_responses[-1]
            return {
                "response": latest_response.content,
                "agent_role": latest_response.agent_role.value,
                "confidence": latest_response.confidence,
                "handoff_suggestion": (
                    latest_response.handoff_decision.next_agent 
                    if latest_response.handoff_decision and latest_response.handoff_decision.should_handoff
                    else None
                ),
                "session_stats": {
                    "total_interactions": result.total_interactions,
                    "agents_used": len(set(result.handoff_chain)),
                    "handoff_chain": [role.value for role in result.handoff_chain]
                }
            }
        
        return {"error": "No response generated"}
    
    def get_constellation_status(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get status of a constellation session."""
        if session_id not in self.constellation_states:
            return None
        
        state = self.constellation_states[session_id]
        return {
            "session_id": session_id,
            "constellation_type": state.constellation_type.value,
            "framework": state.framework.value,
            "module_id": state.module_id,
            "current_agent": state.current_agent.value if state.current_agent else None,
            "total_interactions": state.total_interactions,
            "active_agents": [role.value for role in state.active_agents],
            "session_duration": (datetime.now() - state.session_start_time).total_seconds()
        }
    
    def end_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """End a constellation session and return summary."""
        if session_id not in self.active_constellations:
            return None
        
        state = self.constellation_states[session_id]
        summary = {
            "session_id": session_id,
            "total_interactions": state.total_interactions,
            "agents_used": list(set([role.value for role in state.handoff_chain])),
            "handoff_chain": [role.value for role in state.handoff_chain],
            "session_duration": (datetime.now() - state.session_start_time).total_seconds(),
            "constellation_type": state.constellation_type.value
        }
        
        # Clean up
        del self.active_constellations[session_id]
        del self.constellation_states[session_id]
        
        return summary
    
    def get_active_sessions(self) -> List[str]:
        """Get list of active session IDs."""
        return list(self.active_constellations.keys())
