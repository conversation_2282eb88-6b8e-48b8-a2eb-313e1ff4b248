"""Pytest configuration and fixtures for GAAPF tests."""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock
from pathlib import Path
import tempfile
import shutil

from pyframeworks_assistant.config.user_profiles import User<PERSON><PERSON><PERSON>le, SkillLevel, LearningStyle, LearningPace
from pyframeworks_assistant.config.framework_configs import SupportedFrameworks
from pyframeworks_assistant.core.learning_hub import LearningHubCore
from pyframeworks_assistant.agents.agent_factory import AgentFactory


@pytest.fixture
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def temp_dir():
    """Create a temporary directory for tests."""
    temp_dir = tempfile.mkdtemp()
    yield Path(temp_dir)
    shutil.rmtree(temp_dir)


@pytest.fixture
def mock_llm():
    """Create a mock LLM for testing."""
    llm = AsyncMock()
    llm.ainvoke = AsyncMock(return_value="Mock LLM response")
    llm.invoke = Mock(return_value="Mock LLM response")
    return llm


@pytest.fixture
def sample_user_profile():
    """Create a sample user profile for testing."""
    return UserProfile(
        user_id="test_user",
        name="Test User",
        programming_experience_years=3,
        python_skill_level=SkillLevel.INTERMEDIATE,
        ai_ml_experience=SkillLevel.BEGINNER,
        preferred_learning_style=LearningStyle.HANDS_ON,
        learning_pace=LearningPace.MODERATE,
        learning_goals=["Learn LangChain", "Build RAG applications"]
    )


@pytest.fixture
async def learning_hub(mock_llm, temp_dir):
    """Create a learning hub instance for testing."""
    # Temporarily change data directories to temp directory
    import pyframeworks_assistant.core.temporal_state
    import pyframeworks_assistant.core.knowledge_graph
    import pyframeworks_assistant.core.analytics_engine
    import pyframeworks_assistant.memory.memory_manager
    
    # Patch data directories
    original_temporal_dir = pyframeworks_assistant.core.temporal_state.TemporalStateManager.__init__.__defaults__
    original_knowledge_dir = pyframeworks_assistant.core.knowledge_graph.KnowledgeGraphManager.__init__.__defaults__
    original_analytics_dir = pyframeworks_assistant.core.analytics_engine.AnalyticsEngine.__init__.__defaults__
    original_memory_dir = pyframeworks_assistant.memory.memory_manager.MemoryManager.__init__.__defaults__
    
    try:
        hub = LearningHubCore(mock_llm)
        
        # Override data directories
        hub.temporal_manager.data_dir = temp_dir / "temporal"
        hub.knowledge_graph.data_dir = temp_dir / "knowledge"
        hub.analytics_engine.data_dir = temp_dir / "analytics"
        hub.memory_manager.data_dir = temp_dir / "memory"
        
        # Create directories
        for subdir in ["temporal", "knowledge", "analytics", "memory"]:
            (temp_dir / subdir).mkdir(exist_ok=True)
        
        await hub.initialize()
        yield hub
    
    finally:
        # Restore original defaults
        pass


@pytest.fixture
def agent_factory(mock_llm):
    """Create an agent factory for testing."""
    return AgentFactory(mock_llm)


@pytest.fixture
def sample_session_data():
    """Sample session data for testing."""
    return {
        "duration_minutes": 30.5,
        "interactions_count": 15,
        "agents_used": ["instructor", "code_assistant"],
        "handoff_count": 2,
        "completion_rate": 0.75,
        "user_feedback": {
            "satisfaction": 4,
            "comments": "Great session!"
        },
        "context": {
            "difficulty": "intermediate",
            "topics_covered": ["chains", "prompts"]
        }
    }


@pytest.fixture
def mock_tools():
    """Create mock tools for testing."""
    tools = {}
    
    # Mock search tool
    search_tool = Mock()
    search_tool.name = "tavily_search"
    search_tool.description = "Search the web"
    search_tool._run = Mock(return_value="Mock search results")
    search_tool._arun = AsyncMock(return_value="Mock search results")
    tools["tavily_search"] = search_tool
    
    # Mock file tool
    file_tool = Mock()
    file_tool.name = "file_write"
    file_tool.description = "Write files"
    file_tool._run = Mock(return_value="File written successfully")
    file_tool._arun = AsyncMock(return_value="File written successfully")
    tools["file_write"] = file_tool
    
    return tools


# Test data constants
TEST_FRAMEWORKS = [SupportedFrameworks.LANGCHAIN, SupportedFrameworks.LANGGRAPH]
TEST_MODULES = ["lc_basics", "lc_chains", "lg_basics"]
TEST_CONSTELLATION_TYPES = [
    "knowledge_intensive",
    "hands_on_focused", 
    "theory_practice_balanced",
    "basic_learning",
    "guided_learning"
]
